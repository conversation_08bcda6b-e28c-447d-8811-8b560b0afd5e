# 🔍 Elasticsearch Test Interface

A comprehensive web-based testing interface for the Allora Elasticsearch search system.

## 🚀 Quick Start

### 1. Start Your Backend
Make sure your Allora backend is running:
```bash
cd allora/backend
python run_with_waitress.py
```
Your backend should be accessible at `http://localhost:5000`

### 2. Launch the Test Interface
```bash
cd allora/backend
python serve_test_interface.py
```

The interface will automatically open in your browser at `http://localhost:8080`

## 🎯 Features

### 📊 **Product Search Testing**
- **Basic Search**: Test text-based product search with various queries
- **Advanced Filtering**: Sort by price, rating, popularity, name, etc.
- **Pagination**: Test different page sizes and navigation
- **Real-time Results**: See search results formatted as product cards

### ⚡ **Advanced Search Features**
- **Autocomplete**: Test search suggestions as you type
- **Similar Products**: Find products similar to a specific product ID
- **Complex Boolean Search**: Test advanced query combinations with JSON syntax
- **Search Filters**: Get available filter options dynamically

### 🏥 **System Health Monitoring**
- **Health Checks**: Monitor Elasticsearch connection status
- **Performance Metrics**: View search response times and statistics
- **Connection Testing**: Verify Elasticsearch connectivity
- **System Status**: Real-time system health indicators

### 📈 **Analytics Testing**
- **Search Tracking**: Track user search behavior and patterns
- **Performance Analytics**: Monitor search performance over time
- **User Behavior**: Test analytics data collection
- **Metrics Dashboard**: View comprehensive search analytics

## 🎮 **How to Use**

### Basic Search Test
1. Enter a search query (e.g., "laptop", "phone", "electronics")
2. Select sorting options (relevance, price, rating, etc.)
3. Set pagination preferences
4. Click "🔍 Search Products"
5. View results in the formatted product cards

### Advanced Features Test
1. **Autocomplete**: Type in the autocomplete field and see suggestions
2. **Similar Products**: Enter a product ID to find similar items
3. **Complex Search**: Use JSON syntax for advanced boolean queries
4. **Filters**: Get dynamic filter options based on your data

### System Health Check
1. Click "❤️ Check Health" to verify system status
2. Use "🔌 Test Connection" to check Elasticsearch connectivity
3. Monitor performance with "⚡ Get Performance"

### Analytics Testing
1. Enter search queries to track
2. Provide user IDs for user-specific analytics
3. Track search behavior and view performance metrics

## ⌨️ **Keyboard Shortcuts**

- **Ctrl+Enter**: Execute search from any input field
- **Ctrl+Shift+T**: Run comprehensive test suite
- **Ctrl+Shift+C**: Clear all results
- **Enter**: Execute relevant action for focused input

## 🧪 **Comprehensive Testing**

### Run All Tests
Click "🧪 Run All Tests" to execute a complete test suite that includes:
1. Basic product search
2. Search filters retrieval
3. Autocomplete functionality
4. Similar products search
5. System health check
6. Search suggestions
7. Connection testing
8. Analytics tracking
9. Performance metrics

### Sample Data
Click "📝 Load Sample Data" to populate all fields with test data for quick testing.

## 📊 **Understanding Results**

### Search Results Format
```json
{
  "success": true,
  "data": {
    "products": [...],
    "total": 150,
    "page": 1,
    "per_page": 10,
    "total_pages": 15,
    "aggregations": {...},
    "performance": {
      "total_time_ms": 45,
      "elasticsearch_time_ms": 23
    }
  }
}
```

### Health Check Format
```json
{
  "success": true,
  "data": {
    "elasticsearch_connected": true,
    "fallback_mode": false,
    "response_time_ms": 12,
    "indices_status": "healthy"
  }
}
```

## 🔧 **Configuration**

### Backend URL
The interface connects to `http://localhost:5000` by default. To change this, edit the `API_BASE` variable in the HTML file:

```javascript
const API_BASE = 'http://your-backend-url:port';
```

### Custom Port
Run the interface on a different port:
```bash
python serve_test_interface.py --port 8081
```

### No Auto-Browser
Prevent automatic browser opening:
```bash
python serve_test_interface.py --no-browser
```

## 🐛 **Troubleshooting**

### Common Issues

**❌ "Connection refused" errors**
- Ensure your Allora backend is running on port 5000
- Check that the search_system package is properly installed
- Verify Elasticsearch is running and accessible

**❌ "CORS" errors**
- The test server includes CORS headers automatically
- If issues persist, check your backend CORS configuration

**❌ "404 Not Found" errors**
- Verify the API endpoints exist in your backend
- Check that search_system blueprints are registered

**❌ Empty search results**
- Ensure your database has product data
- Check Elasticsearch indices are created and populated
- Verify index mappings are correct

### Debug Mode
Enable browser developer tools (F12) to see detailed error messages and network requests.

## 📁 **Files Included**

- `elasticsearch_test_interface.html` - Main test interface
- `serve_test_interface.py` - Local web server
- `ELASTICSEARCH_TEST_INTERFACE_README.md` - This documentation

## 🎯 **Testing Scenarios**

### Scenario 1: Basic Functionality
1. Test basic search with common terms
2. Verify pagination works correctly
3. Check sorting options function properly
4. Ensure results display correctly

### Scenario 2: Advanced Features
1. Test autocomplete with partial queries
2. Verify similar products functionality
3. Test complex boolean search queries
4. Check filter options are populated

### Scenario 3: Error Handling
1. Test with invalid product IDs
2. Try malformed JSON in complex search
3. Test with empty queries
4. Verify graceful error handling

### Scenario 4: Performance
1. Test with large result sets
2. Monitor response times
3. Check system health under load
4. Verify analytics tracking works

## 🎉 **Success Indicators**

✅ **All tests pass** in the comprehensive test suite  
✅ **Search results** display properly formatted products  
✅ **Health checks** show Elasticsearch connected  
✅ **Performance metrics** show reasonable response times  
✅ **Analytics tracking** records search behavior  
✅ **Error handling** works gracefully for edge cases  

## 📞 **Support**

If you encounter issues:
1. Check the browser console for error messages
2. Verify your backend is running and accessible
3. Ensure Elasticsearch is properly configured
4. Check the backend logs for detailed error information

Happy testing! 🚀
