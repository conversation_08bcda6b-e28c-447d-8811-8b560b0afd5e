# 🧪 COMPREHENSIVE BACKEND TESTING GUIDE

## 📋 **TESTING STRATEGY OVERVIEW**

**Current Status:** 68.5% success rate (61/89 endpoints working)  
**Goal:** Achieve 90%+ success rate and production readiness  
**Backend URL:** http://localhost:5000  

---

## 🎯 **PHASE 1: BASIC HEALTH & CONNECTIVITY TESTING**

### **Step 1.1: Server Health Check**
```bash
# Test if backend is running
curl http://localhost:5000/api/health

# Expected: {"status": "healthy", "timestamp": "..."}
```

### **Step 1.2: Database Connectivity**
```bash
cd allora/backend
python test_db.py

# Expected: All database models working correctly
```

### **Step 1.3: Basic Endpoint Availability**
```bash
# Test basic endpoints
curl http://localhost:5000/api/products
curl http://localhost:5000/api/categories
curl http://localhost:5000/sitemap.xml
```

---

## 🔍 **PHASE 2: AUTOMATED ENDPOINT TESTING**

### **Step 2.1: Run Comprehensive Endpoint Tests**
```bash
cd allora/backend
python test_all_endpoints.py

# This will test all 89 endpoints and generate detailed report
```

### **Step 2.2: Analyze Test Results**
```bash
# View detailed JSON report
cat api_test_report.json

# Check success rate and failed endpoints
grep -E "(success_rate|failed)" api_test_report.json
```

### **Step 2.3: Identify Critical Failures**
```bash
# Filter only failed endpoints
python -c "
import json
with open('api_test_report.json', 'r') as f:
    data = json.load(f)
    failed = [test for test in data['test_results'] if not test['success']]
    for test in failed:
        print(f'FAILED: {test[\"method\"]} {test[\"endpoint\"]} - {test[\"status_code\"]}')
"
```

---

## 🔐 **PHASE 3: AUTHENTICATION SYSTEM TESTING**

### **Step 3.1: User Registration & Login**
```bash
# Test user registration
curl -X POST http://localhost:5000/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpass123",
    "full_name": "Test User"
  }'

# Test user login
curl -X POST http://localhost:5000/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpass123"
  }'

# Save the returned token for further tests
```

### **Step 3.2: Token-Based Authentication**
```bash
# Test protected endpoint with token
TOKEN="your_jwt_token_here"
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/profile
```

### **Step 3.3: Admin Authentication**
```bash
# Test admin login
curl -X POST http://localhost:5000/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin_password"
  }'
```

### **Step 3.4: Seller Authentication**
```bash
# Test seller registration
curl -X POST http://localhost:5000/api/seller/register \
  -H "Content-Type: application/json" \
  -d '{
    "business_name": "Test Store",
    "email": "<EMAIL>",
    "password": "sellerpass123",
    "contact_person": "John Seller"
  }'
```

---

## 🛒 **PHASE 4: E-COMMERCE FLOW TESTING**

### **Step 4.1: Product Catalog Testing**
```bash
# Test product listing
curl http://localhost:5000/api/products

# Test product details
curl http://localhost:5000/api/products/1

# Test categories
curl http://localhost:5000/api/categories

# Test featured products
curl http://localhost:5000/api/products/featured
```

### **Step 4.2: Search Functionality**
```bash
# Test basic search
curl "http://localhost:5000/api/search?q=laptop"

# Test autocomplete
curl "http://localhost:5000/api/search/autocomplete?q=lap"

# Test advanced search
curl -X POST http://localhost:5000/api/search/complex \
  -H "Content-Type: application/json" \
  -d '{
    "query": "laptop",
    "category": "electronics",
    "min_price": 500,
    "max_price": 2000
  }'
```

### **Step 4.3: Shopping Cart Testing**
```bash
# Test cart operations (requires authentication)
TOKEN="your_jwt_token_here"

# View cart
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/cart

# Add to cart
curl -X POST -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  http://localhost:5000/api/cart \
  -d '{
    "product_id": 1,
    "quantity": 2
  }'

# Update cart
curl -X PUT -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  http://localhost:5000/api/cart \
  -d '{
    "product_id": 1,
    "quantity": 3
  }'
```

### **Step 4.4: Order Management Testing**
```bash
# Test order creation
curl -X POST -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  http://localhost:5000/api/orders \
  -d '{
    "shipping_address": {
      "street": "123 Test St",
      "city": "Test City",
      "state": "TS",
      "zip": "12345"
    },
    "payment_method": "credit_card"
  }'

# Test order history
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/orders

# Test order details
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/orders/1
```

---

## 👤 **PHASE 5: USER ACCOUNT TESTING**

### **Step 5.1: Profile Management**
```bash
# Test profile retrieval
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/profile

# Test profile update
curl -X PUT -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  http://localhost:5000/api/profile \
  -d '{
    "full_name": "Updated Name",
    "phone": "************"
  }'
```

### **Step 5.2: Address Management**
```bash
# Test address listing
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/addresses

# Test add address
curl -X POST -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  http://localhost:5000/api/addresses \
  -d '{
    "address_type": "shipping",
    "full_name": "Test User",
    "address_line_1": "123 Main St",
    "city": "Test City",
    "state": "TS",
    "postal_code": "12345",
    "country": "USA"
  }'
```

### **Step 5.3: Wishlist Testing**
```bash
# Test wishlist
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/wishlist

# Add to wishlist
curl -X POST -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  http://localhost:5000/api/wishlist \
  -d '{
    "product_id": 1
  }'
```

---

## 🏪 **PHASE 6: SELLER PLATFORM TESTING**

### **Step 6.1: Seller Dashboard**
```bash
SELLER_TOKEN="seller_jwt_token_here"

# Test seller dashboard
curl -H "Authorization: Bearer $SELLER_TOKEN" \
  http://localhost:5000/api/seller/dashboard

# Test seller products
curl -H "Authorization: Bearer $SELLER_TOKEN" \
  http://localhost:5000/api/seller/products

# Test seller orders
curl -H "Authorization: Bearer $SELLER_TOKEN" \
  http://localhost:5000/api/seller/orders
```

---

## 👨‍💼 **PHASE 7: ADMIN PANEL TESTING**

### **Step 7.1: Admin Operations**
```bash
ADMIN_TOKEN="admin_jwt_token_here"

# Test admin dashboard
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  http://localhost:5000/api/admin/dashboard

# Test user management
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  http://localhost:5000/api/admin/users

# Test product management
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  http://localhost:5000/api/admin/products
```

---

## 🔧 **PHASE 8: ERROR HANDLING & EDGE CASES**

### **Step 8.1: Authentication Errors**
```bash
# Test invalid token
curl -H "Authorization: Bearer invalid_token" \
  http://localhost:5000/api/profile

# Test missing token
curl http://localhost:5000/api/profile

# Test expired token (if applicable)
```

### **Step 8.2: Input Validation**
```bash
# Test invalid data
curl -X POST http://localhost:5000/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "",
    "email": "invalid-email",
    "password": "123"
  }'
```

### **Step 8.3: Rate Limiting**
```bash
# Test rate limiting (make multiple rapid requests)
for i in {1..20}; do
  curl http://localhost:5000/api/products
done
```

---

## 📊 **PHASE 9: PERFORMANCE TESTING**

### **Step 9.1: Response Time Testing**
```bash
# Test response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:5000/api/products

# Create curl-format.txt:
echo "     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n" > curl-format.txt
```

### **Step 9.2: Load Testing**
```bash
# Install Apache Bench (if not installed)
# Test concurrent requests
ab -n 100 -c 10 http://localhost:5000/api/products
```

---

## 📋 **PHASE 10: COMPREHENSIVE REPORTING**

### **Step 10.1: Generate Test Report**
```bash
cd allora/backend
python -c "
import json
import datetime

# Load test results
with open('api_test_report.json', 'r') as f:
    data = json.load(f)

# Generate summary
total = len(data['test_results'])
passed = sum(1 for test in data['test_results'] if test['success'])
failed = total - passed
success_rate = (passed / total) * 100

print(f'=== BACKEND TEST SUMMARY ===')
print(f'Date: {datetime.datetime.now()}')
print(f'Total Endpoints: {total}')
print(f'Passed: {passed}')
print(f'Failed: {failed}')
print(f'Success Rate: {success_rate:.1f}%')
print(f'Average Response Time: {data[\"summary\"][\"average_response_time\"]:.3f}s')
"
```

### **Step 10.2: Create Action Plan**
```bash
# Identify critical failures for immediate fix
python -c "
import json
with open('api_test_report.json', 'r') as f:
    data = json.load(f)
    
critical_endpoints = [
    '/api/orders', '/api/cart', '/api/wishlist', 
    '/api/addresses', '/api/payment-methods'
]

failed_critical = []
for test in data['test_results']:
    if not test['success'] and any(endpoint in test['endpoint'] for endpoint in critical_endpoints):
        failed_critical.append(test)

print('=== CRITICAL FAILURES TO FIX ===')
for test in failed_critical:
    print(f'{test[\"method\"]} {test[\"endpoint\"]} - Status: {test[\"status_code\"]}')
"
```

---

## 🎯 **SUCCESS CRITERIA**

### **Minimum Acceptable:**
- ✅ **80%+ success rate** (72+ endpoints working)
- ✅ **All authentication endpoints working**
- ✅ **Core e-commerce flow functional**

### **Production Ready:**
- ✅ **90%+ success rate** (81+ endpoints working)
- ✅ **All critical user flows working**
- ✅ **Response times < 500ms**
- ✅ **Proper error handling**

### **Excellent:**
- ✅ **95%+ success rate** (85+ endpoints working)
- ✅ **All endpoints functional**
- ✅ **Response times < 200ms**
- ✅ **Comprehensive error handling**

**Current Status: 68.5% - Needs improvement to reach production readiness** 🎯

---

## 🚀 **QUICK START TESTING COMMANDS**

### **Option 1: Run All Tests (Recommended)**
```bash
cd allora/backend

# 1. Basic health check
python test_db.py

# 2. Comprehensive endpoint testing
python test_all_endpoints.py

# 3. View results
cat ENDPOINT_TEST_RESULTS.md
```

### **Option 2: Manual Step-by-Step Testing**
```bash
# Follow each phase in this guide sequentially
# Start with Phase 1, then Phase 2, etc.
```

### **Option 3: Critical Endpoints Only**
```bash
# Test only the most important endpoints
curl http://localhost:5000/api/health
curl http://localhost:5000/api/products
curl -X POST http://localhost:5000/api/register -H "Content-Type: application/json" -d '{"username":"test","email":"<EMAIL>","password":"test123","full_name":"Test User"}'
```

---

## 📞 **SUPPORT & TROUBLESHOOTING**

### **If Backend Won't Start:**
```bash
cd allora/backend
python run_with_waitress.py
# OR
python app.py
```

### **If Database Issues:**
```bash
python seed_database.py  # Reinitialize database
```

### **If Port Issues:**
```bash
netstat -ano | findstr :5000  # Check what's using port 5000
```

**Ready to start comprehensive backend testing!** 🧪✨
