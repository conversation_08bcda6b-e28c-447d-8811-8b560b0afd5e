"""
Recommendation Analytics and Optimization System
===============================================

Comprehensive analytics system for tracking recommendation performance,
measuring effectiveness, and providing optimization insights.

Features:
- Click-through rate tracking
- Conversion rate analysis
- A/B testing analytics
- Model performance monitoring
- Recommendation effectiveness analysis
- Automated optimization recommendations

Author: Allora Development Team
Date: 2025-07-06
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
from collections import defaultdict, Counter
import json
import sys
import os

# Add backend path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

# Import database models with lazy loading to avoid circular imports
def get_models():
    """Get database models using lazy import"""
    try:
        from app import db, UserInteractionLog, Product, User, UserBehaviorProfile
        return db, UserInteractionLog, Product, User, UserBehaviorProfile
    except ImportError:
        return None, None, None, None, None

class RecommendationAnalytics:
    """
    Analytics system for recommendation performance tracking
    """
    
    def __init__(self, db_session):
        self.db_session = db_session
        
        # Analytics configuration
        self.default_time_window = 30  # days
        self.min_interactions_for_analysis = 10
        self.conversion_events = ['purchase', 'add_to_cart']
        self.engagement_events = ['click', 'view', 'rating', 'wishlist_add']
    
    def get_recommendation_performance_report(self, 
                                            start_date: datetime = None,
                                            end_date: datetime = None,
                                            algorithm: str = None) -> Dict[str, Any]:
        """Generate comprehensive recommendation performance report"""
        try:
            # Set default date range
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=self.default_time_window)
            
            # Get models
            db, UserInteractionLog, Product, User, UserBehaviorProfile = get_models()
            if not db:
                return {'error': 'Database models not available'}
            
            # Get recommendation interactions within date range
            rec_interactions = UserInteractionLog.query.filter(
                UserInteractionLog.created_at >= start_date,
                UserInteractionLog.created_at <= end_date,
                UserInteractionLog.interaction_type.in_(['recommendation_click', 'recommendation_view', 'recommendation_purchase'])
            )
            
            if algorithm:
                rec_interactions = rec_interactions.filter(
                    UserInteractionLog.metadata.contains(f'"algorithm": "{algorithm}"')
                )
            
            rec_interactions = rec_interactions.all()
            
            if len(rec_interactions) < self.min_interactions_for_analysis:
                return {
                    'warning': f'Insufficient data for analysis. Found {len(rec_interactions)} interactions, need at least {self.min_interactions_for_analysis}',
                    'interactions_count': len(rec_interactions)
                }
            
            # Calculate key metrics
            metrics = {
                'overview': self._calculate_overview_metrics(rec_interactions),
                'click_through_rates': self._calculate_ctr_metrics(rec_interactions),
                'conversion_rates': self._calculate_conversion_metrics(rec_interactions),
                'algorithm_performance': self._analyze_algorithm_performance(rec_interactions),
                'user_engagement': self._analyze_user_engagement(rec_interactions),
                'product_performance': self._analyze_product_performance(rec_interactions),
                'temporal_analysis': self._analyze_temporal_patterns(rec_interactions),
                'recommendations': self._generate_optimization_recommendations(rec_interactions)
            }
            
            return {
                'success': True,
                'report_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days
                },
                'algorithm_filter': algorithm,
                'metrics': metrics,
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error generating recommendation performance report: {e}")
            return {'error': f'Failed to generate report: {str(e)}'}
    
    def _calculate_overview_metrics(self, interactions: List) -> Dict[str, Any]:
        """Calculate high-level overview metrics"""
        try:
            if not interactions:
                return {}
            
            # Convert to DataFrame for easier analysis
            interactions_df = pd.DataFrame([{
                'user_id': i.user_id,
                'product_id': i.product_id,
                'interaction_type': i.interaction_type,
                'session_id': i.session_id,
                'created_at': i.created_at,
                'metadata': json.loads(i.metadata) if i.metadata else {}
            } for i in interactions])
            
            # Basic counts
            total_interactions = len(interactions_df)
            unique_users = interactions_df['user_id'].nunique()
            unique_products = interactions_df['product_id'].nunique()
            unique_sessions = interactions_df['session_id'].nunique()
            
            # Recommendation-specific metrics
            rec_requests = interactions_df[interactions_df['interaction_type'] == 'recommendation_view']
            total_recommendations = len(rec_requests)
            
            # Engagement interactions
            engagement_interactions = interactions_df[
                interactions_df['interaction_type'].isin(self.engagement_events + ['recommendation_click'])
            ]
            
            # Conversion interactions
            conversion_interactions = interactions_df[
                interactions_df['interaction_type'].isin(self.conversion_events)
            ]
            
            return {
                'total_interactions': int(total_interactions),
                'unique_users': int(unique_users),
                'unique_products': int(unique_products),
                'unique_sessions': int(unique_sessions),
                'total_recommendations_served': int(total_recommendations),
                'engagement_interactions': len(engagement_interactions),
                'conversion_interactions': len(conversion_interactions),
                'avg_interactions_per_user': round(total_interactions / max(unique_users, 1), 2),
                'avg_interactions_per_session': round(total_interactions / max(unique_sessions, 1), 2)
            }
            
        except Exception as e:
            logging.error(f"Error calculating overview metrics: {e}")
            return {}
    
    def _calculate_ctr_metrics(self, interactions: List) -> Dict[str, Any]:
        """Calculate click-through rate metrics"""
        try:
            if not interactions:
                return {}
            
            interactions_df = pd.DataFrame([{
                'user_id': i.user_id,
                'product_id': i.product_id,
                'interaction_type': i.interaction_type,
                'session_id': i.session_id,
                'created_at': i.created_at,
                'metadata': json.loads(i.metadata) if i.metadata else {}
            } for i in interactions])
            
            # Get recommendation views and clicks
            rec_views = interactions_df[interactions_df['interaction_type'] == 'recommendation_view']
            rec_clicks = interactions_df[interactions_df['interaction_type'] == 'recommendation_click']
            
            if len(rec_views) == 0:
                return {'error': 'No recommendation views found'}
            
            # Overall CTR
            overall_ctr = (len(rec_clicks) / len(rec_views)) * 100
            
            # CTR by product
            product_views = rec_views['product_id'].value_counts()
            product_clicks = rec_clicks['product_id'].value_counts()
            product_ctr = {}
            
            for product_id in product_views.index:
                clicks = product_clicks.get(product_id, 0)
                views = product_views[product_id]
                product_ctr[int(product_id)] = {
                    'views': int(views),
                    'clicks': int(clicks),
                    'ctr': round((clicks / views) * 100, 2)
                }
            
            # CTR by user
            user_views = rec_views['user_id'].value_counts()
            user_clicks = rec_clicks['user_id'].value_counts()
            user_ctr_distribution = []
            
            for user_id in user_views.index:
                clicks = user_clicks.get(user_id, 0)
                views = user_views[user_id]
                ctr = (clicks / views) * 100
                user_ctr_distribution.append(ctr)
            
            return {
                'overall_ctr': round(overall_ctr, 2),
                'total_views': len(rec_views),
                'total_clicks': len(rec_clicks),
                'product_ctr': dict(list(product_ctr.items())[:10]),  # Top 10 products
                'user_ctr_stats': {
                    'mean': round(np.mean(user_ctr_distribution), 2),
                    'median': round(np.median(user_ctr_distribution), 2),
                    'std': round(np.std(user_ctr_distribution), 2),
                    'min': round(np.min(user_ctr_distribution), 2),
                    'max': round(np.max(user_ctr_distribution), 2)
                }
            }
            
        except Exception as e:
            logging.error(f"Error calculating CTR metrics: {e}")
            return {}
    
    def _calculate_conversion_metrics(self, interactions: List) -> Dict[str, Any]:
        """Calculate conversion rate metrics"""
        try:
            if not interactions:
                return {}
            
            interactions_df = pd.DataFrame([{
                'user_id': i.user_id,
                'product_id': i.product_id,
                'interaction_type': i.interaction_type,
                'session_id': i.session_id,
                'created_at': i.created_at,
                'value': getattr(i, 'value', 0),
                'metadata': json.loads(i.metadata) if i.metadata else {}
            } for i in interactions])
            
            # Get recommendation requests and conversions
            rec_requests = interactions_df[interactions_df['interaction_type'] == 'recommendation_view']
            conversions = interactions_df[
                interactions_df['interaction_type'].isin(self.conversion_events)
            ]
            
            if len(rec_requests) == 0:
                return {'error': 'No recommendation requests found'}
            
            # Calculate overall conversion rate
            total_recommendations = rec_requests['value'].sum()
            total_conversions = len(conversions)
            overall_conversion_rate = (total_conversions / max(total_recommendations, 1)) * 100
            
            # Conversion by type
            conversion_by_type = conversions['interaction_type'].value_counts().to_dict()
            
            # Revenue from conversions (if available)
            purchase_conversions = conversions[conversions['interaction_type'] == 'purchase']
            total_revenue = purchase_conversions['value'].sum() if not purchase_conversions.empty else 0
            
            # Average order value
            avg_order_value = (total_revenue / max(len(purchase_conversions), 1)) if total_revenue > 0 else 0
            
            return {
                'overall_conversion_rate': round(overall_conversion_rate, 2),
                'total_recommendations': int(total_recommendations),
                'total_conversions': int(total_conversions),
                'conversion_by_type': {k: int(v) for k, v in conversion_by_type.items()},
                'revenue_metrics': {
                    'total_revenue': float(total_revenue),
                    'avg_order_value': round(avg_order_value, 2),
                    'revenue_per_recommendation': round(total_revenue / max(total_recommendations, 1), 2)
                }
            }
            
        except Exception as e:
            logging.error(f"Error calculating conversion metrics: {e}")
            return {}

    def _analyze_algorithm_performance(self, interactions: List) -> Dict[str, Any]:
        """Analyze performance by recommendation algorithm"""
        try:
            if not interactions:
                return {}

            # Group interactions by algorithm
            algorithm_stats = defaultdict(lambda: {
                'requests': 0,
                'clicks': 0,
                'conversions': 0,
                'revenue': 0,
                'unique_users': set(),
                'unique_products': set()
            })

            for interaction in interactions:
                try:
                    metadata = json.loads(interaction.metadata) if interaction.metadata else {}
                    algorithm = metadata.get('algorithm', 'unknown')

                    stats = algorithm_stats[algorithm]

                    if interaction.interaction_type == 'recommendation_view':
                        stats['requests'] += 1
                    elif interaction.interaction_type == 'recommendation_click':
                        stats['clicks'] += 1
                    elif interaction.interaction_type in self.conversion_events:
                        stats['conversions'] += 1
                        stats['revenue'] += getattr(interaction, 'value', 0)

                    stats['unique_users'].add(interaction.user_id)
                    if interaction.product_id:
                        stats['unique_products'].add(interaction.product_id)

                except Exception:
                    continue

            # Calculate performance metrics
            performance = {}
            for algorithm, stats in algorithm_stats.items():
                requests = stats['requests']
                clicks = stats['clicks']
                conversions = stats['conversions']
                revenue = stats['revenue']

                performance[algorithm] = {
                    'requests': requests,
                    'clicks': clicks,
                    'conversions': conversions,
                    'revenue': round(revenue, 2),
                    'unique_users': len(stats['unique_users']),
                    'unique_products': len(stats['unique_products']),
                    'ctr': round((clicks / max(requests, 1)) * 100, 2),
                    'conversion_rate': round((conversions / max(requests, 1)) * 100, 2),
                    'revenue_per_request': round(revenue / max(requests, 1), 2)
                }

            return performance

        except Exception as e:
            logging.error(f"Error analyzing algorithm performance: {e}")
            return {}

    def _analyze_user_engagement(self, interactions: List) -> Dict[str, Any]:
        """Analyze user engagement patterns"""
        try:
            if not interactions:
                return {}

            # Convert to DataFrame for easier analysis
            interactions_df = pd.DataFrame([{
                'user_id': i.user_id,
                'interaction_type': i.interaction_type,
                'session_id': i.session_id,
                'product_id': i.product_id,
                'timestamp': i.created_at
            } for i in interactions])

            # User engagement metrics
            user_stats = interactions_df.groupby('user_id').agg({
                'interaction_type': 'count',
                'timestamp': ['min', 'max'],
                'session_id': 'nunique',
                'product_id': 'nunique'
            }).round(2)

            # Flatten column names
            user_stats.columns = ['total_interactions', 'first_interaction', 'last_interaction', 'sessions', 'unique_products']

            # Calculate engagement scores
            user_stats['session_duration'] = (user_stats['last_interaction'] - user_stats['first_interaction']).dt.total_seconds() / 3600  # hours
            user_stats['interactions_per_session'] = user_stats['total_interactions'] / user_stats['sessions']

            # Engagement segments
            engagement_segments = {
                'high_engagement': len(user_stats[user_stats['total_interactions'] >= 20]),
                'medium_engagement': len(user_stats[(user_stats['total_interactions'] >= 5) & (user_stats['total_interactions'] < 20)]),
                'low_engagement': len(user_stats[user_stats['total_interactions'] < 5])
            }

            return {
                'total_users': len(user_stats),
                'avg_interactions_per_user': round(user_stats['total_interactions'].mean(), 2),
                'avg_sessions_per_user': round(user_stats['sessions'].mean(), 2),
                'avg_products_per_user': round(user_stats['unique_products'].mean(), 2),
                'avg_interactions_per_session': round(user_stats['interactions_per_session'].mean(), 2),
                'engagement_segments': engagement_segments
            }

        except Exception as e:
            logging.error(f"Error analyzing user engagement: {e}")
            return {}

    def _analyze_product_performance(self, interactions: List) -> Dict[str, Any]:
        """Analyze product performance in recommendations"""
        try:
            if not interactions:
                return {}

            # Convert to DataFrame for easier analysis
            interactions_df = pd.DataFrame([{
                'product_id': i.product_id,
                'interaction_type': i.interaction_type,
                'user_id': i.user_id,
                'session_id': i.session_id
            } for i in interactions if i.product_id])

            if interactions_df.empty:
                return {'error': 'No product interactions found'}

            # Product performance metrics
            product_stats = interactions_df.groupby('product_id').agg({
                'interaction_type': 'count',
                'user_id': 'nunique',
                'session_id': 'nunique'
            }).round(2)

            product_stats.columns = ['total_interactions', 'unique_users', 'unique_sessions']

            # Get top performing products
            top_products = product_stats.sort_values('total_interactions', ascending=False).head(10)

            # Get models for category analysis
            db, UserInteractionLog, Product, User, UserBehaviorProfile = get_models()
            category_performance = {}

            if Product:
                for product_id in interactions_df['product_id'].unique():
                    try:
                        product = Product.query.get(product_id)
                        if product and product.category:
                            if product.category not in category_performance:
                                category_performance[product.category] = 0
                            category_performance[product.category] += len(
                                interactions_df[interactions_df['product_id'] == product_id]
                            )
                    except:
                        continue

            return {
                'total_products_recommended': len(product_stats),
                'avg_interactions_per_product': round(product_stats['total_interactions'].mean(), 2),
                'avg_users_per_product': round(product_stats['unique_users'].mean(), 2),
                'top_products': {int(k): v for k, v in top_products.to_dict('index').items()},
                'category_performance': dict(sorted(category_performance.items(), key=lambda x: x[1], reverse=True))
            }

        except Exception as e:
            logging.error(f"Error analyzing product performance: {e}")
            return {}

    def _analyze_temporal_patterns(self, interactions: List) -> Dict[str, Any]:
        """Analyze temporal patterns in recommendation interactions"""
        try:
            if not interactions:
                return {}

            # Convert to DataFrame for easier analysis
            interactions_df = pd.DataFrame([{
                'timestamp': i.created_at,
                'interaction_type': i.interaction_type
            } for i in interactions])

            # Convert timestamp to datetime if it's not already
            interactions_df['timestamp'] = pd.to_datetime(interactions_df['timestamp'])

            # Daily patterns
            interactions_df['hour'] = interactions_df['timestamp'].dt.hour
            interactions_df['day_of_week'] = interactions_df['timestamp'].dt.day_name()
            interactions_df['date'] = interactions_df['timestamp'].dt.date

            # Hourly distribution
            hourly_distribution = interactions_df['hour'].value_counts().sort_index().to_dict()

            # Daily distribution
            daily_distribution = interactions_df['day_of_week'].value_counts().to_dict()

            # Daily trend
            daily_trend = interactions_df.groupby('date').size().to_dict()
            daily_trend = {str(k): v for k, v in daily_trend.items()}  # Convert date to string

            return {
                'hourly_distribution': hourly_distribution,
                'daily_distribution': daily_distribution,
                'daily_trend': daily_trend,
                'peak_hour': max(hourly_distribution, key=hourly_distribution.get) if hourly_distribution else None,
                'peak_day': max(daily_distribution, key=daily_distribution.get) if daily_distribution else None
            }

        except Exception as e:
            logging.error(f"Error analyzing temporal patterns: {e}")
            return {}

    def _generate_optimization_recommendations(self, interactions: List) -> List[Dict[str, Any]]:
        """Generate automated optimization recommendations"""
        try:
            recommendations = []

            if not interactions:
                return recommendations

            # Analyze algorithm performance for recommendations
            algorithm_performance = self._analyze_algorithm_performance(interactions)

            if algorithm_performance:
                # Find best performing algorithm
                best_algorithm = max(
                    algorithm_performance.keys(),
                    key=lambda x: algorithm_performance[x].get('conversion_rate', 0)
                )

                best_ctr = algorithm_performance[best_algorithm].get('ctr', 0)

                recommendations.append({
                    'type': 'algorithm_optimization',
                    'priority': 'high',
                    'title': f'Optimize Algorithm Distribution',
                    'description': f'{best_algorithm} shows the best performance with {best_ctr}% CTR. Consider increasing its usage.',
                    'impact': 'high',
                    'effort': 'medium'
                })

            # Check for low engagement
            user_engagement = self._analyze_user_engagement(interactions)
            if user_engagement.get('avg_interactions_per_user', 0) < 5:
                recommendations.append({
                    'type': 'engagement_improvement',
                    'priority': 'medium',
                    'title': 'Improve User Engagement',
                    'description': 'Average user interactions are low. Consider improving recommendation relevance or UI/UX.',
                    'impact': 'high',
                    'effort': 'high'
                })

            # Check conversion rates
            conversion_metrics = self._calculate_conversion_metrics(interactions)
            if conversion_metrics.get('overall_conversion_rate', 0) < 2:
                recommendations.append({
                    'type': 'conversion_optimization',
                    'priority': 'high',
                    'title': 'Optimize Conversion Funnel',
                    'description': 'Conversion rate is below 2%. Review recommendation quality and checkout process.',
                    'impact': 'high',
                    'effort': 'medium'
                })

            # Temporal optimization
            temporal_patterns = self._analyze_temporal_patterns(interactions)
            if temporal_patterns.get('peak_hour'):
                recommendations.append({
                    'type': 'temporal_optimization',
                    'priority': 'low',
                    'title': 'Optimize for Peak Hours',
                    'description': f'Peak activity at hour {temporal_patterns["peak_hour"]}. Consider personalized timing for recommendations.',
                    'impact': 'medium',
                    'effort': 'low'
                })

            return recommendations

        except Exception as e:
            logging.error(f"Error generating optimization recommendations: {e}")
            return []

    def get_ab_test_results(self, test_name: str, start_date: datetime = None,
                           end_date: datetime = None) -> Dict[str, Any]:
        """Analyze A/B test results for recommendation algorithms"""
        try:
            # Set default date range
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=self.default_time_window)

            # Get models
            db, UserInteractionLog, Product, User, UserBehaviorProfile = get_models()
            if not db:
                return {'error': 'Database models not available'}

            # Get interactions for the test period
            interactions = UserInteractionLog.query.filter(
                UserInteractionLog.created_at >= start_date,
                UserInteractionLog.created_at <= end_date,
                UserInteractionLog.interaction_type.in_(['recommendation_click', 'recommendation_view', 'recommendation_purchase'])
            ).all()

            if not interactions:
                return {'error': 'No data found for A/B test analysis'}

            # Analyze by algorithm (variant)
            algorithm_performance = self._analyze_algorithm_performance(interactions)

            # Statistical significance testing would go here
            # For now, we'll provide basic comparison

            results = {
                'test_name': test_name,
                'test_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'variants': algorithm_performance,
                'winner': max(algorithm_performance.keys(),
                            key=lambda x: algorithm_performance[x].get('conversion_rate', 0)) if algorithm_performance else None,
                'statistical_significance': 'Not calculated',  # Would implement proper statistical testing
                'recommendation': 'Continue monitoring for more data'
            }

            return results

        except Exception as e:
            logging.error(f"Error analyzing A/B test results: {e}")
            return {'error': str(e)}

# Export main class
__all__ = ['RecommendationAnalytics']
