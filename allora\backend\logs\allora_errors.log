2025-07-11 18:36:00 - __main__ - ERROR - Error getting count for SearchAnalytics: (mysql.connector.errors.ProgrammingError) 1054 (42S22): Unknown column 'search_analytics.guest_session_id' in 'field list'
[SQL: SELECT count(*) AS count_1 
FROM (SELECT search_analytics.id AS search_analytics_id, search_analytics.user_id AS search_analytics_user_id, search_analytics.guest_session_id AS search_analytics_guest_session_id, search_analytics.search_query AS search_analytics_search_query, search_analytics.search_type AS search_analytics_search_type, search_analytics.results_count AS search_analytics_results_count, search_analytics.filters_applied AS search_analytics_filters_applied, search_analytics.clicked_results AS search_analytics_clicked_results, search_analytics.session_id AS search_analytics_session_id, search_analytics.ip_address AS search_analytics_ip_address, search_analytics.user_agent AS search_analytics_user_agent, search_analytics.created_at AS search_analytics_created_at 
FROM search_analytics) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-11 18:51:23 - __main__ - ERROR - Error getting table info for SearchAnalytics: (mysql.connector.errors.ProgrammingError) 1054 (42S22): Unknown column 'search_analytics.guest_session_id' in 'field list'
[SQL: SELECT count(*) AS count_1 
FROM (SELECT search_analytics.id AS search_analytics_id, search_analytics.user_id AS search_analytics_user_id, search_analytics.guest_session_id AS search_analytics_guest_session_id, search_analytics.search_query AS search_analytics_search_query, search_analytics.search_type AS search_analytics_search_type, search_analytics.results_count AS search_analytics_results_count, search_analytics.filters_applied AS search_analytics_filters_applied, search_analytics.clicked_results AS search_analytics_clicked_results, search_analytics.session_id AS search_analytics_session_id, search_analytics.ip_address AS search_analytics_ip_address, search_analytics.user_agent AS search_analytics_user_agent, search_analytics.created_at AS search_analytics_created_at 
FROM search_analytics) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-11 18:56:35 - __main__ - ERROR - Error getting count for SearchAnalytics: (mysql.connector.errors.ProgrammingError) 1054 (42S22): Unknown column 'search_analytics.guest_session_id' in 'field list'
[SQL: SELECT count(*) AS count_1 
FROM (SELECT search_analytics.id AS search_analytics_id, search_analytics.user_id AS search_analytics_user_id, search_analytics.guest_session_id AS search_analytics_guest_session_id, search_analytics.search_query AS search_analytics_search_query, search_analytics.search_type AS search_analytics_search_type, search_analytics.results_count AS search_analytics_results_count, search_analytics.filters_applied AS search_analytics_filters_applied, search_analytics.clicked_results AS search_analytics_clicked_results, search_analytics.session_id AS search_analytics_session_id, search_analytics.ip_address AS search_analytics_ip_address, search_analytics.user_agent AS search_analytics_user_agent, search_analytics.created_at AS search_analytics_created_at 
FROM search_analytics) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-11 22:21:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:29:19 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:31:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:32:44 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:33:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:34:45 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:55:59 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:57:22 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:58:13 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:00:20 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:00:59 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:01:40 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:02:20 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:28:11 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:28:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:29:54 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:31:51 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:33:01 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:34:02 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:35:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:36:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:38:28 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:39:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:40:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:42:02 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:43:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:45:21 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:49:49 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:51:06 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:52:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:54:16 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:55:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:57:21 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:59:01 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:00:22 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:14:54 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:16:22 - allora - ERROR - Failed to import advanced visual search model: No module named 'visual_search_model'
2025-07-12 00:16:22 - allora - ERROR - Failed to import advanced visual search model: No module named 'visual_search_model'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:17:08 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:17:08 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:19:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:19:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 16:57:47 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 16:57:48 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 16:57:48 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:02:06 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:02:06 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:02:06 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Frontend Error: 
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:51 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:06:51 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:06:51 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:06:52 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:06:52 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:06:52 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:06:54 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:06:54 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:06:54 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:54 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    return jsonify({'error': 'Missing required refund information'}), 400
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:54 - allora - ERROR - Frontend Error: 
2025-07-12 17:06:54 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
    }
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:51 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:15:51 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:15:51 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:15:52 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:15:52 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    def addresses():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    def addresses():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    def payment_methods():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    def recently_viewed():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    def cart():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    def cart():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    """Get smart bundle recommendations based on current cart contents"""
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def orders():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def orders():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    def order_detail(order_id):
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    def wishlist():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    def wishlist():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    if request.method == 'POST':
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    order = Order.query.filter_by(id=order_id, user_id=user.id).first_or_404()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
    except Exception as e:
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Frontend Error: 
2025-07-12 17:16:38 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:16:38 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    def cart():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:57 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:23:57 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:23:57 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    def addresses():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    def addresses():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    })
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    return jsonify({
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    return jsonify({'message': 'Payment method deleted successfully'})
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    return jsonify({'message': 'Payment method deleted successfully'})
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    db.session.commit()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    # Order Management Endpoints
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    # Order Management Endpoints
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    db.session.rollback()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    def wishlist():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    def wishlist():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    if request.method == 'POST':
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:23:59 - allora - ERROR - Frontend Error: 
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
    'permissions': {
ValueError: too many values to unpack (expected 2)
2025-07-12 17:24:00 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    order_id = data.get('order_id')
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:04 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:28:04 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:28:05 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    # Update product average rating
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    # Update type-specific fields
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    # Update type-specific fields
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    cart_item = CartItem.query.filter_by(id=cart_item_id, user_id=user.id).first()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    wishlist_item = Wishlist.query.filter_by(id=item_id, user_id=user.id).first()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    wishlist_item = Wishlist.query.filter_by(id=item_id, user_id=user.id).first()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    # Add user interaction data if user is authenticated
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    'product_name': item.product_name,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
    return jsonify({
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Frontend Error: 
2025-07-12 17:31:48 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:31:48 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    # Update type-specific fields
ValueError: too many values to unpack (expected 2)
2025-07-12 17:32:05 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:32:05 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    # Update type-specific fields
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:27 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:38:27 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:38:27 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    'created_at': new_order.created_at.isoformat(),
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    # Check if user already reviewed this product
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    'nickname': payment_method.nickname,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    'nickname': payment_method.nickname,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    cart_item = CartItem.query.filter_by(user_id=user.id, product_id=product_id).first()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def move_wishlist_to_cart(item_id):
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def move_wishlist_to_cart(item_id):
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    # Generate order number
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    # Add user interaction data if user is authenticated
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    order_items = OrderItem.query.filter_by(order_id=invoice.order_id).all()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Frontend Error: 
2025-07-12 17:42:21 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:42:21 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:42:21 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    'created_at': new_order.created_at.isoformat(),
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    # Check if user already reviewed this product
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    'nickname': payment_method.nickname,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    'nickname': payment_method.nickname,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    cart_item = CartItem.query.filter_by(user_id=user.id, product_id=product_id).first()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def move_wishlist_to_cart(item_id):
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def move_wishlist_to_cart(item_id):
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    # Generate order number
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    # Add user interaction data if user is authenticated
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:42:24 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    order_items = OrderItem.query.filter_by(order_id=invoice.order_id).all()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:24 - allora - ERROR - Frontend Error: 
2025-07-12 17:42:24 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
ValueError: too many values to unpack (expected 2)
2025-07-12 18:29:20 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 20:24:00 - elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 20:31:35 - elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 20:31:35 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 20:31:35 - elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 20:32:16 - elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 20:32:16 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 20:32:16 - elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 21:30:20 - search_system.elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 22:02:22 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:28 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 22:02:28 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 22:02:32 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:33 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:33 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:33 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:34 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:35 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:39 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:42 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:43 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:47 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:47 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:48 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:48 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:48 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:48 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:49 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:49 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:49 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:49 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:49 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:50 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:03:00 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:03:01 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:03:03 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:03:04 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:03:07 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 22:03:28 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:04:28 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:06:31 - search_system.elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:06:34 - search_system.elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:06:35 - search_system.elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 22:07:01 - search_system.elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:28:31 - search_system.elasticsearch_manager - ERROR - Error reindexing products: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
