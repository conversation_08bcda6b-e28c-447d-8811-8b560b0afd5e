#!/usr/bin/env python3
"""
Create Categories Table Script
=============================

This script creates the categories table according to the project's structure
and populates it with the standard categories used throughout the system.

Based on analysis of the codebase, the categories are:
- Electronics, Fashion, Home & Garden, Sports & Outdoors, Books & Media
- Health & Beauty, Toys & Games, Automotive, Food & Beverages, Baby & Kids

Features:
- Hierarchical category structure (parent-child relationships)
- SEO-friendly slugs
- Category metadata (description, image, etc.)
- Integration with existing product categories
- Proper indexing for performance

Usage:
    python create_categories_table.py [--populate]
"""

import os
import sys
import argparse
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

def create_categories_table():
    """Create the categories table with proper structure"""
    print("🏗️  CREATING CATEGORIES TABLE")
    print("=" * 40)
    
    with app.app_context():
        try:
            with db.engine.connect() as connection:
                # Drop existing table if it exists
                print("🗑️  Dropping existing categories table if exists...")
                connection.execute(text("DROP TABLE IF EXISTS categories"))
                
                # Create categories table
                create_table_sql = """
                CREATE TABLE categories (
                    id INTEGER AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    slug VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT NULL,
                    parent_id INTEGER NULL,
                    level INTEGER NOT NULL DEFAULT 0,
                    sort_order INTEGER NOT NULL DEFAULT 0,
                    image_url VARCHAR(500) NULL,
                    icon_class VARCHAR(100) NULL,
                    meta_title VARCHAR(200) NULL,
                    meta_description VARCHAR(500) NULL,
                    is_active TINYINT NOT NULL DEFAULT 1,
                    is_featured TINYINT NOT NULL DEFAULT 0,
                    product_count INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    -- Foreign key constraint for hierarchical structure
                    CONSTRAINT fk_categories_parent_id 
                        FOREIGN KEY (parent_id) REFERENCES categories(id) 
                        ON DELETE SET NULL ON UPDATE CASCADE,
                    
                    -- Indexes for performance
                    INDEX idx_categories_parent_id (parent_id),
                    INDEX idx_categories_slug (slug),
                    INDEX idx_categories_name (name),
                    INDEX idx_categories_is_active (is_active),
                    INDEX idx_categories_is_featured (is_featured),
                    INDEX idx_categories_level (level),
                    INDEX idx_categories_sort_order (sort_order),
                    INDEX idx_categories_created_at (created_at)
                )
                """
                
                print("🏗️  Creating categories table...")
                connection.execute(text(create_table_sql))
                connection.commit()
                
                print("✅ Categories table created successfully!")
                return True
                
        except Exception as e:
            print(f"❌ Error creating categories table: {e}")
            return False

def populate_categories_table():
    """Populate the categories table with standard categories"""
    print("\n📊 POPULATING CATEGORIES TABLE")
    print("=" * 40)
    
    # Main categories based on project analysis
    main_categories = [
        {
            'name': 'Electronics',
            'slug': 'electronics',
            'description': 'Sustainable and eco-friendly electronic devices and accessories',
            'icon_class': 'fas fa-laptop',
            'sort_order': 1,
            'is_featured': True,
            'subcategories': ['Smartphones', 'Laptops', 'Tablets', 'Headphones', 'Cameras', 'Smart Watches']
        },
        {
            'name': 'Fashion',
            'slug': 'fashion',
            'description': 'Sustainable clothing, accessories, and fashion items',
            'icon_class': 'fas fa-tshirt',
            'sort_order': 2,
            'is_featured': True,
            'subcategories': ['Men\'s Clothing', 'Women\'s Clothing', 'Shoes', 'Accessories', 'Jewelry', 'Bags']
        },
        {
            'name': 'Home & Garden',
            'slug': 'home-garden',
            'description': 'Eco-friendly home decor, furniture, and garden supplies',
            'icon_class': 'fas fa-home',
            'sort_order': 3,
            'is_featured': True,
            'subcategories': ['Furniture', 'Kitchen', 'Bedding', 'Decor', 'Garden Tools', 'Lighting']
        },
        {
            'name': 'Sports & Outdoors',
            'slug': 'sports-outdoors',
            'description': 'Sustainable sports equipment and outdoor gear',
            'icon_class': 'fas fa-running',
            'sort_order': 4,
            'is_featured': False,
            'subcategories': ['Fitness Equipment', 'Outdoor Gear', 'Sports Apparel', 'Team Sports', 'Water Sports']
        },
        {
            'name': 'Books & Media',
            'slug': 'books-media',
            'description': 'Books, educational materials, and digital media',
            'icon_class': 'fas fa-book',
            'sort_order': 5,
            'is_featured': False,
            'subcategories': ['Fiction', 'Non-Fiction', 'Educational', 'Comics', 'Movies', 'Music']
        },
        {
            'name': 'Health & Beauty',
            'slug': 'health-beauty',
            'description': 'Natural and organic health and beauty products',
            'icon_class': 'fas fa-heart',
            'sort_order': 6,
            'is_featured': True,
            'subcategories': ['Skincare', 'Makeup', 'Hair Care', 'Supplements', 'Personal Care', 'Fragrances']
        },
        {
            'name': 'Toys & Games',
            'slug': 'toys-games',
            'description': 'Educational and eco-friendly toys and games',
            'icon_class': 'fas fa-gamepad',
            'sort_order': 7,
            'is_featured': False,
            'subcategories': ['Action Figures', 'Board Games', 'Educational Toys', 'Video Games', 'Puzzles']
        },
        {
            'name': 'Automotive',
            'slug': 'automotive',
            'description': 'Eco-friendly automotive parts and accessories',
            'icon_class': 'fas fa-car',
            'sort_order': 8,
            'is_featured': False,
            'subcategories': ['Car Parts', 'Accessories', 'Tools', 'Maintenance', 'Electric Vehicle']
        },
        {
            'name': 'Food & Beverages',
            'slug': 'food-beverages',
            'description': 'Organic and sustainable food and beverage products',
            'icon_class': 'fas fa-utensils',
            'sort_order': 9,
            'is_featured': False,
            'subcategories': ['Organic Food', 'Beverages', 'Snacks', 'Supplements', 'Specialty Items']
        },
        {
            'name': 'Baby & Kids',
            'slug': 'baby-kids',
            'description': 'Safe and eco-friendly products for babies and children',
            'icon_class': 'fas fa-baby',
            'sort_order': 10,
            'is_featured': True,
            'subcategories': ['Baby Care', 'Kids Clothing', 'Toys', 'Furniture', 'Safety Items']
        }
    ]
    
    with app.app_context():
        try:
            with db.engine.connect() as connection:
                print("📝 Inserting main categories...")
                
                # Insert main categories first
                category_ids = {}
                for i, category in enumerate(main_categories, 1):
                    insert_sql = """
                    INSERT INTO categories 
                    (name, slug, description, parent_id, level, sort_order, icon_class, 
                     is_active, is_featured, meta_title, meta_description)
                    VALUES 
                    (:name, :slug, :description, NULL, 0, :sort_order, :icon_class, 
                     1, :is_featured, :meta_title, :meta_description)
                    """
                    
                    meta_title = f"{category['name']} - Sustainable Shopping"
                    meta_description = f"Shop sustainable {category['name'].lower()} products. {category['description']}"
                    
                    result = connection.execute(text(insert_sql), {
                        'name': category['name'],
                        'slug': category['slug'],
                        'description': category['description'],
                        'sort_order': category['sort_order'],
                        'icon_class': category['icon_class'],
                        'is_featured': 1 if category['is_featured'] else 0,
                        'meta_title': meta_title,
                        'meta_description': meta_description
                    })
                    
                    category_ids[category['name']] = result.lastrowid
                    print(f"  ✅ {category['name']} (ID: {result.lastrowid})")
                
                print(f"\n📝 Inserting subcategories...")
                
                # Insert subcategories
                subcategory_count = 0
                for category in main_categories:
                    parent_id = category_ids[category['name']]
                    
                    for j, subcategory in enumerate(category['subcategories'], 1):
                        # Create unique slug by including parent category
                        base_slug = subcategory.lower().replace(' ', '-').replace('\'', '').replace('&', 'and')
                        parent_slug = category['slug']
                        subcategory_slug = f"{parent_slug}-{base_slug}"

                        insert_sql = """
                        INSERT INTO categories
                        (name, slug, description, parent_id, level, sort_order,
                         is_active, meta_title, meta_description)
                        VALUES
                        (:name, :slug, :description, :parent_id, 1, :sort_order,
                         1, :meta_title, :meta_description)
                        """

                        meta_title = f"{subcategory} - {category['name']} - Sustainable Shopping"
                        meta_description = f"Shop sustainable {subcategory.lower()} in our {category['name'].lower()} category."
                        
                        connection.execute(text(insert_sql), {
                            'name': subcategory,
                            'slug': subcategory_slug,
                            'description': f"Sustainable {subcategory.lower()} products",
                            'parent_id': parent_id,
                            'sort_order': j,
                            'meta_title': meta_title,
                            'meta_description': meta_description
                        })
                        
                        subcategory_count += 1
                
                connection.commit()
                
                print(f"  ✅ {subcategory_count} subcategories inserted")
                
                # Update product counts
                print(f"\n📊 Updating product counts...")
                update_product_counts_sql = """
                UPDATE categories c 
                SET product_count = (
                    SELECT COUNT(*) 
                    FROM products p 
                    WHERE p.category = c.name
                )
                WHERE c.level = 0
                """
                
                connection.execute(text(update_product_counts_sql))
                connection.commit()
                
                print("✅ Product counts updated")
                
                # Show summary
                result = connection.execute(text("SELECT COUNT(*) FROM categories WHERE level = 0"))
                main_count = result.fetchone()[0]
                
                result = connection.execute(text("SELECT COUNT(*) FROM categories WHERE level = 1"))
                sub_count = result.fetchone()[0]
                
                result = connection.execute(text("SELECT SUM(product_count) FROM categories WHERE level = 0"))
                total_products = result.fetchone()[0] or 0
                
                print(f"\n📊 CATEGORIES SUMMARY:")
                print(f"  Main Categories: {main_count}")
                print(f"  Subcategories: {sub_count}")
                print(f"  Total Categories: {main_count + sub_count}")
                print(f"  Products Linked: {total_products}")
                
                return True
                
        except Exception as e:
            print(f"❌ Error populating categories: {e}")
            return False

def verify_categories_table():
    """Verify the categories table was created correctly"""
    print(f"\n✅ VERIFYING CATEGORIES TABLE")
    print("=" * 40)
    
    with app.app_context():
        try:
            with db.engine.connect() as connection:
                # Check table structure
                result = connection.execute(text("DESCRIBE categories"))
                columns = result.fetchall()
                
                print(f"📋 Table Structure ({len(columns)} columns):")
                for col in columns:
                    print(f"  • {col[0]:<20} {str(col[1]):<20} {str(col[2])}")
                
                # Check data
                result = connection.execute(text("""
                    SELECT 
                        (SELECT COUNT(*) FROM categories WHERE level = 0) as main_categories,
                        (SELECT COUNT(*) FROM categories WHERE level = 1) as subcategories,
                        (SELECT COUNT(*) FROM categories WHERE is_featured = 1) as featured,
                        (SELECT COUNT(*) FROM categories WHERE is_active = 1) as active
                """))
                
                stats = result.fetchone()
                
                print(f"\n📊 Data Summary:")
                print(f"  Main Categories: {stats[0]}")
                print(f"  Subcategories: {stats[1]}")
                print(f"  Featured Categories: {stats[2]}")
                print(f"  Active Categories: {stats[3]}")
                
                # Show sample categories
                result = connection.execute(text("""
                    SELECT name, slug, level, is_featured, product_count 
                    FROM categories 
                    WHERE level = 0 
                    ORDER BY sort_order 
                    LIMIT 5
                """))
                
                print(f"\n📋 Sample Main Categories:")
                for cat in result.fetchall():
                    featured = "⭐" if cat[3] else "  "
                    print(f"  {featured} {cat[0]:<20} ({cat[1]:<15}) - {cat[4]} products")
                
                return True
                
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Create and populate categories table')
    parser.add_argument('--populate', action='store_true', help='Populate with standard categories')
    
    args = parser.parse_args()
    
    print("🏗️  CATEGORIES TABLE CREATION")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = True
    
    # Step 1: Create table
    if not create_categories_table():
        success = False
    
    # Step 2: Populate if requested
    if success and args.populate:
        if not populate_categories_table():
            success = False
    
    # Step 3: Verify
    if success:
        if not verify_categories_table():
            success = False
    
    if success:
        print(f"\n🎉 SUCCESS: Categories table created and ready!")
        if args.populate:
            print("✅ Table populated with standard categories")
        print("\n📋 NEXT STEPS:")
        print("1. Categories table is now available for use")
        print("2. Products can reference categories by name")
        print("3. Run comprehensive database test to verify")
    else:
        print(f"\n❌ FAILED: Some issues occurred")
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
