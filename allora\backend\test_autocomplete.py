#!/usr/bin/env python3
"""
Test autocomplete functionality with human-readable output
"""

import sys
sys.path.append('.')

from app import app
from search_system.elasticsearch_search import get_search_engine

def main():
    """Test autocomplete functionality and display results in a human-readable format"""
    with app.app_context():
        engine = get_search_engine()
        
        # Test different queries including problematic ones
        test_queries = ['lapt', 'laptop', 'sam', 'samsung']
        
        print("=" * 50)
        print("Autocomplete Test Results")
        print("=" * 50)
        
        for query in test_queries:
            print(f"\n🔍 Testing autocomplete for query: '{query}'")
            suggestions = engine.autocomplete_suggestions(query, 10)
            
            if not suggestions:
                print("   No suggestions found.")
                continue
                
            print(f"   Found {len(suggestions)} suggestion(s):")
            print("-" * 40)
            
            for i, suggestion in enumerate(suggestions[:5], 1):
                text = suggestion.get('text', 'N/A')
                type_ = suggestion.get('type', 'Unknown')
                score = suggestion.get('score', 0)
                print(f"   {i}. {text}")
                print(f"      Type: {type_}")
                print(f"      Score: {score:.2f}")
                print("-" * 40)
        
        # Test the specific query from your issue
        print(f"\n🎯 Testing specific query: 'lap'")
        suggestions = engine.autocomplete_suggestions('lap', 10)
        
        if not suggestions:
            print("   No suggestions found for 'lap'.")
        else:
            print(f"   Found {len(suggestions)} suggestion(s):")
            print("-" * 40)
            for i, suggestion in enumerate(suggestions[:5], 1):
                text = suggestion.get('text', 'N/A')
                type_ = suggestion.get('type', 'Unknown')
                score = suggestion.get('score', 0)
                print(f"   {i}. {text}")
                print(f"      Type: {type_}")
                print(f"      Score: {score:.2f}")
                print("-" * 40)
        
        print("\nTest completed.")

if __name__ == "__main__":
    main()