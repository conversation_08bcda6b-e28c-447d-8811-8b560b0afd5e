"""
RMA API Endpoints
================

Comprehensive API endpoints for Return Merchandise Authorization (RMA) system.
Provides both customer-facing and admin APIs for RMA management.

API Categories:
1. Customer RMA APIs - Create, track, and manage returns
2. Admin RMA APIs - Approve, process, and manage all RMAs
3. RMA Status APIs - Real-time status tracking
4. RMA Analytics APIs - Reporting and analytics
5. RMA Configuration APIs - System configuration

Authentication:
- Customer APIs: JWT token required
- Admin APIs: Admin JWT token required
- Public APIs: No authentication (limited functionality)
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from functools import wraps
import logging
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add backend path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import RMA components
from .rma_engine import RMAEngine, RMAProcessingError, RMAValidationError
from .rma_utils import RMAValidator, RMACalculator, RMAReportGenerator
from .rma_architecture import RMAStatus, RMAType, ReturnReason

# Database models will be imported lazily to avoid circular imports
def get_rma_models():
    """Get RMA database models using lazy import"""
    try:
        from app import (
            db, RMARequest, RMAItem, RMATimeline, RMADocument,
            RMAApproval, RMARule, RMAConfiguration, RMAStats,
            User, Product, Order, OrderItem
        )
        return (
            db, RMARequest, RMAItem, RMATimeline, RMADocument,
            RMAApproval, RMARule, RMAConfiguration, RMAStats,
            User, Product, Order, OrderItem
        )
    except ImportError as e:
        logger.error(f"Failed to import RMA models: {e}")
        return tuple([None] * 12)

def with_rma_models(func):
    """Decorator to inject RMA database models into function"""
    def wrapper(*args, **kwargs):
        # Get models and inject them into the function's globals
        models = get_rma_models()
        if models[0] is None:  # db is None
            return jsonify({'error': 'Database models not available'}), 500

        (db, RMARequest, RMAItem, RMATimeline, RMADocument,
         RMAApproval, RMARule, RMAConfiguration, RMAStats,
         User, Product, Order, OrderItem) = models

        # Inject models into function's global namespace
        func.__globals__['db'] = db
        func.__globals__['RMARequest'] = RMARequest
        func.__globals__['RMAItem'] = RMAItem
        func.__globals__['RMATimeline'] = RMATimeline
        func.__globals__['RMADocument'] = RMADocument
        func.__globals__['RMAApproval'] = RMAApproval
        func.__globals__['RMARule'] = RMARule
        func.__globals__['RMAConfiguration'] = RMAConfiguration
        func.__globals__['RMAStats'] = RMAStats
        func.__globals__['User'] = User
        func.__globals__['Product'] = Product
        func.__globals__['Order'] = Order
        func.__globals__['OrderItem'] = OrderItem

        return func(*args, **kwargs)
    return wrapper

# Database models will be imported lazily to avoid circular imports
def get_models():
    """Get database models using lazy import"""
    try:
        from app import db, RMARequest, RMAItem, RMATimeline, Order, OrderItem, User
        return db, RMARequest, RMAItem, RMATimeline, Order, OrderItem, User
    except ImportError:
        return None, None, None, None, None, None, None

logger = logging.getLogger(__name__)

# Create Blueprint
rma_bp = Blueprint('rma', __name__, url_prefix='/api/rma')

def admin_required(f):
    """Decorator to require admin authentication"""
    @wraps(f)
    @jwt_required()
    def decorated_function(*args, **kwargs):
        claims = get_jwt()
        if not claims.get('is_admin', False):
            return jsonify({'error': 'Admin access required'}), 403
        return f(*args, **kwargs)
    return decorated_function

# ============================================================================
# CUSTOMER RMA APIs
# ============================================================================

@rma_bp.route('/create', methods=['POST'])
@jwt_required()
def create_rma_request():
    """Create a new RMA request"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['order_id', 'items', 'customer_info']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'{field} is required'}), 400
        
        # Get database models
        db, RMARequest, RMAItem, RMATimeline, Order, OrderItem, User = get_models()
        if not db:
            return jsonify({'error': 'Database not available'}), 500
        
        # Validate order belongs to user
        order = Order.query.filter_by(id=data['order_id'], user_id=user_id).first()
        if not order:
            return jsonify({'error': 'Order not found or access denied'}), 404
        
        # Validate return eligibility
        from .rma_architecture import RMA_CONFIG
        is_eligible, message = RMAValidator.validate_return_eligibility(order, RMA_CONFIG)
        if not is_eligible:
            return jsonify({'error': message}), 400
        
        # Validate return items
        order_items = OrderItem.query.filter_by(order_id=order.id).all()
        is_valid, message = RMAValidator.validate_return_items(order_items, data['items'])
        if not is_valid:
            return jsonify({'error': message}), 400
        
        # Create RMA request
        rma_engine = RMAEngine(db.session)
        rma_number = rma_engine.create_rma_request(
            order_id=data['order_id'],
            items=data['items'],
            customer_info=data['customer_info'],
            rma_type=data.get('rma_type', 'return_refund')
        )
        
        return jsonify({
            'success': True,
            'rma_number': rma_number,
            'message': 'RMA request created successfully'
        }), 201
        
    except RMAValidationError as e:
        return jsonify({'error': str(e)}), 400
    except RMAProcessingError as e:
        return jsonify({'error': str(e)}), 500
    except Exception as e:
        logger.error(f"Error creating RMA request: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/my-requests', methods=['GET'])
@jwt_required()
def get_user_rma_requests():
    """Get user's RMA requests"""
    try:
        user_id = get_jwt_identity()
        
        # Get database models
        db, RMARequest, RMAItem, RMATimeline, Order, OrderItem, User = get_models()
        if not db:
            return jsonify({'error': 'Database not available'}), 500
        
        # Query parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 10, type=int), 100)
        status = request.args.get('status')
        
        # Build query
        query = RMARequest.query.filter_by(user_id=user_id)
        
        if status:
            query = query.filter_by(status=status)
        
        # Execute query with pagination
        rma_requests = query.order_by(RMARequest.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Format response
        requests_data = []
        for rma_request in rma_requests.items:
            requests_data.append({
                'rma_number': rma_request.rma_number,
                'order_id': rma_request.order_id,
                'status': rma_request.status,
                'rma_type': rma_request.rma_type,
                'total_refund_amount': float(rma_request.total_refund_amount),
                'created_at': rma_request.created_at.isoformat(),
                'updated_at': rma_request.updated_at.isoformat(),
                'deadline': rma_request.deadline.isoformat() if rma_request.deadline else None
            })
        
        return jsonify({
            'success': True,
            'requests': requests_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': rma_requests.total,
                'pages': rma_requests.pages,
                'has_next': rma_requests.has_next,
                'has_prev': rma_requests.has_prev
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting user RMA requests: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@rma_bp.route('/<rma_number>', methods=['GET'])
@jwt_required()
def get_rma_details(rma_number):
    """Get detailed RMA information"""
    try:
        user_id = get_jwt_identity()
        claims = get_jwt()
        is_admin = claims.get('is_admin', False)

        # Get database models
        db, RMARequest, RMAItem, RMATimeline, Order, OrderItem, User = get_models()
        if not db:
            return jsonify({'error': 'Database not available'}), 500

        # Build query based on user role
        if is_admin:
            rma_request = RMARequest.query.filter_by(rma_number=rma_number).first()
        else:
            rma_request = RMARequest.query.filter_by(
                rma_number=rma_number, user_id=user_id
            ).first()

        if not rma_request:
            return jsonify({'error': 'RMA request not found'}), 404

        # Get RMA items
        rma_items = RMAItem.query.filter_by(rma_request_id=rma_request.id).all()

        # Format response
        items_data = []
        for item in rma_items:
            items_data.append({
                'id': item.id,
                'order_item_id': item.order_item_id,
                'product_id': item.product_id,
                'quantity': item.quantity,
                'unit_price': float(item.unit_price),
                'total_price': float(item.total_price),
                'return_reason': item.return_reason,
                'condition_notes': item.condition_notes,
                'inspection_result': item.inspection_result,
                'inspection_notes': item.inspection_notes,
                'item_status': item.item_status
            })

        response_data = {
            'rma_number': rma_request.rma_number,
            'order_id': rma_request.order_id,
            'status': rma_request.status,
            'rma_type': rma_request.rma_type,
            'customer_email': rma_request.customer_email,
            'customer_phone': rma_request.customer_phone,
            'customer_notes': rma_request.customer_notes,
            'total_refund_amount': float(rma_request.total_refund_amount),
            'restocking_fee': float(rma_request.restocking_fee),
            'return_shipping_cost': float(rma_request.return_shipping_cost),
            'requires_approval': rma_request.requires_approval,
            'approved_by': rma_request.approved_by,
            'approved_at': rma_request.approved_at.isoformat() if rma_request.approved_at else None,
            'rejection_reason': rma_request.rejection_reason,
            'return_label_url': rma_request.return_label_url,
            'return_tracking_number': rma_request.return_tracking_number,
            'return_carrier': rma_request.return_carrier,
            'inspection_result': rma_request.inspection_result,
            'inspection_notes': rma_request.inspection_notes,
            'inspected_by': rma_request.inspected_by,
            'inspected_at': rma_request.inspected_at.isoformat() if rma_request.inspected_at else None,
            'refund_method': rma_request.refund_method,
            'refund_reference': rma_request.refund_reference,
            'refunded_at': rma_request.refunded_at.isoformat() if rma_request.refunded_at else None,
            'created_at': rma_request.created_at.isoformat(),
            'updated_at': rma_request.updated_at.isoformat(),
            'deadline': rma_request.deadline.isoformat() if rma_request.deadline else None,
            'items': items_data
        }

        return jsonify({
            'success': True,
            'rma_request': response_data
        }), 200

    except Exception as e:
        logger.error(f"Error getting RMA details: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# ============================================================================
# ADMIN RMA APIs
# ============================================================================

@rma_bp.route('/admin/requests', methods=['GET'])
@admin_required
def get_all_rma_requests():
    """Get all RMA requests (admin only)"""
    try:
        # Get database models
        db, RMARequest, RMAItem, RMATimeline, Order, OrderItem, User = get_models()
        if not db:
            return jsonify({'error': 'Database not available'}), 500

        # Query parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        status = request.args.get('status')
        rma_type = request.args.get('rma_type')

        # Build query
        query = RMARequest.query

        if status:
            query = query.filter_by(status=status)
        if rma_type:
            query = query.filter_by(rma_type=rma_type)

        # Execute query with pagination
        rma_requests = query.order_by(RMARequest.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # Format response
        requests_data = []
        for rma_request in rma_requests.items:
            requests_data.append({
                'rma_number': rma_request.rma_number,
                'order_id': rma_request.order_id,
                'user_id': rma_request.user_id,
                'status': rma_request.status,
                'rma_type': rma_request.rma_type,
                'customer_email': rma_request.customer_email,
                'total_refund_amount': float(rma_request.total_refund_amount),
                'requires_approval': rma_request.requires_approval,
                'created_at': rma_request.created_at.isoformat(),
                'updated_at': rma_request.updated_at.isoformat(),
                'deadline': rma_request.deadline.isoformat() if rma_request.deadline else None
            })

        return jsonify({
            'success': True,
            'requests': requests_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': rma_requests.total,
                'pages': rma_requests.pages,
                'has_next': rma_requests.has_next,
                'has_prev': rma_requests.has_prev
            }
        }), 200

    except Exception as e:
        logger.error(f"Error getting all RMA requests: {e}")
        return jsonify({'error': 'Internal server error'}), 500
