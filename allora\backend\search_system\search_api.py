"""
Advanced Search API Endpoints
Provides comprehensive search functionality with Elasticsearch backend and analytics tracking
"""

import logging
import time
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from typing import Dict, Any, Optional

from .elasticsearch_search import get_search_engine
from .elasticsearch_manager import get_elasticsearch_manager
from .search_analytics_tracker import search_tracker, SearchEvent

logger = logging.getLogger(__name__)

# Create search blueprint
search_bp = Blueprint('search', __name__, url_prefix='/api/search')

@search_bp.route('/', methods=['GET'])
def search_products():
    """
    Advanced product search with filters, sorting, and pagination
    
    Query Parameters:
    - q: Search query string
    - categories: Comma-separated category names
    - brands: Comma-separated brand names
    - sellers: Comma-separated seller names
    - price_min: Minimum price
    - price_max: Maximum price
    - min_rating: Minimum rating (1-5)
    - min_sustainability: Minimum sustainability score (0-100)
    - in_stock_only: Filter to in-stock products only (true/false)
    - verified_sellers_only: Filter to verified sellers only (true/false)
    - free_shipping_only: Filter to free shipping products only (true/false)
    - organic_only: Filter to organic products only (true/false)
    - fair_trade_only: Filter to fair trade products only (true/false)
    - recyclable_only: Filter to recyclable products only (true/false)
    - on_sale_only: Filter to discounted products only (true/false)
    - sort_by: Sort field (relevance, price, rating, popularity, newest, name, sustainability, reviews)
    - sort_order: Sort order (asc, desc)
    - page: Page number (default: 1)
    - per_page: Results per page (default: 20, max: 100)
    - include_aggregations: Include faceted search aggregations (true/false, default: true)
    """
    try:
        # Get search parameters
        query = request.args.get('q', '').strip()
        
        # Build filters from query parameters
        filters = {}
        
        # Category filters
        if request.args.get('categories'):
            filters['categories'] = [cat.strip() for cat in request.args.get('categories').split(',')]
        
        # Brand filters
        if request.args.get('brands'):
            filters['brands'] = [brand.strip() for brand in request.args.get('brands').split(',')]
        
        # Seller filters
        if request.args.get('sellers'):
            filters['sellers'] = [seller.strip() for seller in request.args.get('sellers').split(',')]
        
        # Price range filters
        if request.args.get('price_min'):
            try:
                filters['price_min'] = float(request.args.get('price_min'))
            except ValueError:
                pass
        
        if request.args.get('price_max'):
            try:
                filters['price_max'] = float(request.args.get('price_max'))
            except ValueError:
                pass
        
        # Rating filter
        if request.args.get('min_rating'):
            try:
                filters['min_rating'] = float(request.args.get('min_rating'))
            except ValueError:
                pass
        
        # Sustainability filter
        if request.args.get('min_sustainability'):
            try:
                filters['min_sustainability'] = int(request.args.get('min_sustainability'))
            except ValueError:
                pass
        
        # Boolean filters
        boolean_filters = [
            'in_stock_only', 'verified_sellers_only', 'free_shipping_only',
            'organic_only', 'fair_trade_only', 'recyclable_only', 'on_sale_only'
        ]
        
        for filter_name in boolean_filters:
            if request.args.get(filter_name):
                filters[filter_name] = request.args.get(filter_name).lower() == 'true'
        
        # Sorting parameters
        sort_by = request.args.get('sort_by', 'relevance')
        sort_order = request.args.get('sort_order', 'desc')
        
        # Pagination parameters
        try:
            page = max(1, int(request.args.get('page', 1)))
        except ValueError:
            page = 1
        
        try:
            per_page = min(100, max(1, int(request.args.get('per_page', 20))))
        except ValueError:
            per_page = 20
        
        # Aggregations parameter
        include_aggregations = request.args.get('include_aggregations', 'true').lower() == 'true'
        
        # Get user ID if available (from session/auth)
        user_id = None  # TODO: Get from authentication context
        session_id = request.headers.get('X-Session-ID', str(uuid.uuid4()))

        # Track search start time
        search_start_time = time.time()

        # Execute search using advanced query builder
        search_engine = get_search_engine()
        results = search_engine.advanced_search_with_query_builder(
            query=query,
            filters=filters,
            sort_by=sort_by,
            sort_order=sort_order,
            page=page,
            per_page=per_page,
            include_aggregations=include_aggregations,
            user_id=user_id
        )

        # Calculate search performance
        total_time_ms = (time.time() - search_start_time) * 1000
        elasticsearch_time_ms = results.get('performance', {}).get('elasticsearch_time_ms', 0)

        # Track search analytics
        try:
            search_event = SearchEvent(
                event_id=str(uuid.uuid4()),
                user_id=user_id,
                session_id=session_id,
                query=query,
                filters=filters,
                search_type='advanced',
                timestamp=datetime.utcnow(),
                results_count=results.get('total', 0),
                response_time_ms=total_time_ms,
                elasticsearch_time_ms=elasticsearch_time_ms,
                user_agent=request.headers.get('User-Agent'),
                ip_address=request.remote_addr
            )
            search_tracker.track_search(search_event)

            # Add search event ID to results for click tracking
            results['search_event_id'] = search_event.event_id

        except Exception as analytics_error:
            logger.warning(f"Failed to track search analytics: {analytics_error}")

        # Add performance metrics to results
        results['performance'] = {
            'total_time_ms': total_time_ms,
            'elasticsearch_time_ms': elasticsearch_time_ms,
            'query_builder': 'advanced'
        }
        
        # Update pagination info
        results['page'] = page
        results['per_page'] = per_page
        results['has_next'] = page < results['total_pages']
        results['has_prev'] = page > 1

        # Add spell suggestions if there are few or no results
        if query and (results.get('total', 0) < 3):
            try:
                spell_suggestions = search_engine.get_spell_suggestions(query, 3)
                if spell_suggestions:
                    results['spell_suggestions'] = spell_suggestions
                    results['original_query'] = query
            except Exception as spell_error:
                logger.warning(f"Failed to get spell suggestions: {spell_error}")

        return jsonify({
            'success': True,
            'data': results
        })
        
    except Exception as e:
        logger.error(f"Search API error: {e}")
        return jsonify({
            'success': False,
            'error': 'Search failed',
            'message': str(e)
        }), 500

@search_bp.route('/autocomplete', methods=['GET'])
def autocomplete():
    """
    Get autocomplete suggestions for search queries
    
    Query Parameters:
    - q: Partial search query
    - limit: Maximum number of suggestions (default: 10, max: 20)
    """
    try:
        query = request.args.get('q', '').strip()
        
        if not query or len(query) < 2:
            return jsonify({
                'success': True,
                'data': {
                    'suggestions': []
                }
            })
        
        try:
            limit = min(20, max(1, int(request.args.get('limit', 10))))
        except ValueError:
            limit = 10
        
        search_engine = get_search_engine()
        suggestions = search_engine.autocomplete_suggestions(query, limit)

        # If we have few suggestions, try to get spell corrections
        spell_suggestions = []
        if len(suggestions) < 5:  # Increased threshold for spell suggestions
            spell_suggestions = search_engine.get_spell_suggestions(query, 3)

            # Add manual spell corrections for common compound words
            if not spell_suggestions and query:
                query_lower = query.lower().strip()
                manual_corrections = {
                    'ecofriendly': 'eco-friendly',
                    'eco friendly': 'eco-friendly',
                    'biodegradable': 'bio-degradable',
                    'organicfood': 'organic food',
                    'bamboofiber': 'bamboo fiber'
                }

                if query_lower in manual_corrections:
                    spell_suggestions = [manual_corrections[query_lower]]

        return jsonify({
            'success': True,
            'data': {
                'query': query,
                'suggestions': suggestions,
                'spell_suggestions': spell_suggestions,
                'original_query': query if spell_suggestions else None
            }
        })
        
    except Exception as e:
        logger.error(f"Autocomplete API error: {e}")
        return jsonify({
            'success': False,
            'error': 'Autocomplete failed',
            'message': str(e)
        }), 500

@search_bp.route('/suggestions', methods=['GET'])
def search_suggestions():
    """
    Get search query suggestions based on popular searches
    
    Query Parameters:
    - q: Partial search query
    - limit: Maximum number of suggestions (default: 5, max: 10)
    """
    try:
        query = request.args.get('q', '').strip()
        
        if not query or len(query) < 2:
            return jsonify({
                'success': True,
                'data': {
                    'suggestions': []
                }
            })
        
        try:
            limit = min(10, max(1, int(request.args.get('limit', 5))))
        except ValueError:
            limit = 5
        
        search_engine = get_search_engine()
        suggestions = search_engine.search_suggestions(query, limit)
        
        return jsonify({
            'success': True,
            'data': {
                'query': query,
                'suggestions': suggestions
            }
        })
        
    except Exception as e:
        logger.error(f"Search suggestions API error: {e}")
        return jsonify({
            'success': False,
            'error': 'Search suggestions failed',
            'message': str(e)
        }), 500

@search_bp.route('/similar/<int:product_id>', methods=['GET'])
def similar_products(product_id: int):
    """
    Get similar products for a given product
    
    Path Parameters:
    - product_id: ID of the product to find similar products for
    
    Query Parameters:
    - limit: Maximum number of similar products (default: 10, max: 20)
    """
    try:
        try:
            limit = min(20, max(1, int(request.args.get('limit', 10))))
        except ValueError:
            limit = 10
        
        search_engine = get_search_engine()
        similar = search_engine.similar_products(product_id, limit)
        
        return jsonify({
            'success': True,
            'data': {
                'product_id': product_id,
                'similar_products': similar
            }
        })
        
    except Exception as e:
        logger.error(f"Similar products API error: {e}")
        return jsonify({
            'success': False,
            'error': 'Similar products search failed',
            'message': str(e)
        }), 500

@search_bp.route('/filters', methods=['GET'])
def get_search_filters():
    """
    Get available search filters and their options
    """
    try:
        search_engine = get_search_engine()
        
        # Get aggregations without search query to get all available options
        results = search_engine.search_products(
            query="",
            filters={},
            page=1,
            per_page=1,  # We only need aggregations
            include_aggregations=True
        )
        
        filters = {
            'categories': results.get('aggregations', {}).get('categories', []),
            'brands': results.get('aggregations', {}).get('brands', []),
            'sellers': results.get('aggregations', {}).get('sellers', []),
            'price_ranges': results.get('aggregations', {}).get('price_ranges', {}),
            'rating_ranges': results.get('aggregations', {}).get('rating_ranges', {}),
            'sustainability_ranges': results.get('aggregations', {}).get('sustainability_ranges', {}),
            'eco_features': results.get('aggregations', {}).get('eco_features', {}),
            'price_stats': results.get('aggregations', {}).get('price_stats', {})
        }
        
        return jsonify({
            'success': True,
            'data': {
                'filters': filters
            }
        })
        
    except Exception as e:
        logger.error(f"Search filters API error: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to get search filters',
            'message': str(e)
        }), 500

@search_bp.route('/health', methods=['GET'])
def search_health():
    """Check search system health"""
    try:
        es_manager = get_elasticsearch_manager()
        
        # Check Elasticsearch connection
        if not es_manager.es.ping():
            return jsonify({
                'success': False,
                'error': 'Elasticsearch connection failed'
            }), 503
        
        # Get index statistics
        stats = es_manager.get_index_stats()
        
        return jsonify({
            'success': True,
            'data': {
                'elasticsearch_connected': True,
                'indices': stats
            }
        })
        
    except Exception as e:
        logger.error(f"Search health check error: {e}")
        return jsonify({
            'success': False,
            'error': 'Health check failed',
            'message': str(e)
        }), 500

@search_bp.route('/complex', methods=['POST'])
def complex_boolean_search():
    """
    Complex boolean search with multiple search terms and operators

    Request Body:
    {
        "search_terms": [
            {
                "query": "organic cotton",
                "operator": "must",
                "fields": ["name", "description"],
                "boost": 2.0
            },
            {
                "query": "sustainable",
                "operator": "should",
                "fields": ["category", "search_keywords"],
                "boost": 1.5
            },
            {
                "query": "plastic",
                "operator": "must_not",
                "fields": ["material"]
            }
        ],
        "filters": {
            "categories": ["clothing", "accessories"],
            "price_min": 10.0,
            "price_max": 100.0,
            "min_rating": 4.0,
            "in_stock_only": true
        },
        "sort_by": "relevance",
        "sort_order": "desc",
        "page": 1,
        "per_page": 20
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'Request body is required'
            }), 400

        search_terms = data.get('search_terms', [])
        filters = data.get('filters', {})
        sort_by = data.get('sort_by', 'relevance')
        sort_order = data.get('sort_order', 'desc')

        try:
            page = max(1, int(data.get('page', 1)))
        except ValueError:
            page = 1

        try:
            per_page = min(100, max(1, int(data.get('per_page', 20))))
        except ValueError:
            per_page = 20

        if not search_terms:
            return jsonify({
                'success': False,
                'error': 'At least one search term is required'
            }), 400

        # Validate search terms
        for i, term in enumerate(search_terms):
            if not isinstance(term, dict):
                return jsonify({
                    'success': False,
                    'error': f'Search term {i} must be an object'
                }), 400

            if not term.get('query'):
                return jsonify({
                    'success': False,
                    'error': f'Search term {i} must have a query'
                }), 400

            operator = term.get('operator', 'must')
            if operator not in ['must', 'should', 'must_not']:
                return jsonify({
                    'success': False,
                    'error': f'Search term {i} operator must be one of: must, should, must_not'
                }), 400

        # Get user ID and session ID
        user_id = None  # TODO: Get from authentication context
        session_id = request.headers.get('X-Session-ID', str(uuid.uuid4()))

        # Track search start time
        search_start_time = time.time()

        search_engine = get_search_engine()
        results = search_engine.complex_boolean_search(
            search_terms=search_terms,
            filters=filters,
            sort_by=sort_by,
            sort_order=sort_order,
            page=page,
            per_page=per_page
        )

        # Calculate search performance
        total_time_ms = (time.time() - search_start_time) * 1000
        elasticsearch_time_ms = results.get('performance', {}).get('elasticsearch_time_ms', 0)

        # Track search analytics for complex search
        try:
            # Combine search terms into a single query string for analytics
            combined_query = ' '.join([term.get('query', '') for term in search_terms])

            search_event = SearchEvent(
                event_id=str(uuid.uuid4()),
                user_id=user_id,
                session_id=session_id,
                query=combined_query,
                filters=filters,
                search_type='complex',
                timestamp=datetime.utcnow(),
                results_count=results.get('total', 0),
                response_time_ms=total_time_ms,
                elasticsearch_time_ms=elasticsearch_time_ms,
                user_agent=request.headers.get('User-Agent'),
                ip_address=request.remote_addr
            )
            search_tracker.track_search(search_event)

            # Add search event ID to results for click tracking
            results['search_event_id'] = search_event.event_id

        except Exception as analytics_error:
            logger.warning(f"Failed to track complex search analytics: {analytics_error}")

        # Add performance metrics to results
        results['performance'] = {
            'total_time_ms': total_time_ms,
            'elasticsearch_time_ms': elasticsearch_time_ms,
            'query_builder': 'complex'
        }

        return jsonify({
            'success': True,
            'data': results
        })

    except Exception as e:
        logger.error(f"Complex boolean search API error: {e}")
        return jsonify({
            'success': False,
            'error': 'Complex search failed',
            'message': str(e)
        }), 500
