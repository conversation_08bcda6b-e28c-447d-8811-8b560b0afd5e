"""
Search System Package
====================

This package contains all Elasticsearch-related functionality for the Allora e-commerce platform.

Components:
- elasticsearch_config: Configuration and connection management
- elasticsearch_manager: Index management and data synchronization
- elasticsearch_search: Advanced search functionality
- search_api: REST API endpoints for search
- search_analytics_api: Analytics API endpoints
- search_analytics_tracker: Search analytics tracking
- search_query_builder: Query building utilities

Usage:
    from elasticsearch.elasticsearch_config import get_elasticsearch_client
    from elasticsearch.elasticsearch_search import get_search_engine
    from elasticsearch.search_api import search_bp
"""

# Import main components for easy access
from .elasticsearch_config import (
    ElasticsearchConfig,
    get_elasticsearch_client,
    get_product_index_name,
    get_search_analytics_index_name,
    get_suggestions_index_name
)

from .elasticsearch_manager import (
    ElasticsearchManager,
    get_elasticsearch_manager,
    get_elasticsearch_sync_manager
)

from .elasticsearch_search import (
    AdvancedSearchEngine,
    get_search_engine
)

from .search_api import search_bp
from .search_analytics_api import search_analytics_bp
from .search_analytics_tracker import (
    SearchEvent,
    SearchEventData,
    search_tracker
)

from .search_query_builder import (
    SearchQueryBuilder,
    ProductSearchQueryBuilder,
    QueryType,
    BoolOperator,
    SearchField,
    RangeFilter,
    SortField,
    FunctionScore
)

__all__ = [
    # Configuration
    'ElasticsearchConfig',
    'get_elasticsearch_client',
    'get_product_index_name',
    'get_search_analytics_index_name',
    'get_suggestions_index_name',
    
    # Management
    'ElasticsearchManager',
    'get_elasticsearch_manager',
    'get_elasticsearch_sync_manager',
    
    # Search
    'AdvancedSearchEngine',
    'get_search_engine',
    
    # API Blueprints
    'search_bp',
    'search_analytics_bp',
    
    # Analytics
    'SearchEvent',
    'SearchEventData',
    'search_tracker',
    
    # Query Building
    'SearchQueryBuilder',
    'ProductSearchQueryBuilder',
    'QueryType',
    'BoolOperator',
    'SearchField',
    'RangeFilter',
    'SortField',
    'FunctionScore'
]
