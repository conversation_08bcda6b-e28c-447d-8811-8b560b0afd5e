-- Database Backup Before Fixes
-- Created: 2025-07-13 11:46:14
-- Tables: 89

USE allora_db;

-- Table: abandoned_cart
CREATE TABLE `abandoned_cart` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `email` varchar(120) NOT NULL,
  `cart_data` json NOT NULL,
  `cart_total` float NOT NULL,
  `recovery_token` varchar(200) NOT NULL,
  `created_at` datetime NOT NULL,
  `last_reminder_sent` datetime DEFAULT NULL,
  `reminder_count` int NOT NULL,
  `is_recovered` tinyint(1) NOT NULL,
  `recovered_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `recovery_token` (`recovery_token`),
  KEY `user_id` (`user_id`),
  KEY `idx_abandoned_cart_email` (`email`),
  KEY `idx_abandoned_cart_created_at` (`created_at`),
  CONSTRAINT `abandoned_cart_ibfk_1` FOREI<PERSON>N KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: admin_activity_log
CREATE TABLE `admin_activity_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `admin_id` int NOT NULL,
  `action` varchar(100) NOT NULL,
  `resource_type` varchar(50) NOT NULL,
  `resource_id` int DEFAULT NULL,
  `details` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`),
  KEY `idx_admin_activity_log_created_at` (`created_at`),
  CONSTRAINT `admin_activity_log_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: admin_user
CREATE TABLE `admin_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(120) NOT NULL,
  `password` varchar(128) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `role` varchar(20) NOT NULL,
  `can_manage_products` tinyint(1) DEFAULT NULL,
  `can_manage_orders` tinyint(1) DEFAULT NULL,
  `can_manage_users` tinyint(1) DEFAULT NULL,
  `can_view_analytics` tinyint(1) DEFAULT NULL,
  `can_manage_content` tinyint(1) DEFAULT NULL,
  `can_manage_inventory` tinyint(1) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `created_by` (`created_by`),
  KEY `idx_admin_user_created_at` (`created_at`),
  CONSTRAINT `admin_user_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: availability_notification
CREATE TABLE `availability_notification` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `product_id` int NOT NULL,
  `variant_id` int DEFAULT NULL,
  `email` varchar(120) NOT NULL,
  `is_notified` tinyint(1) NOT NULL,
  `created_at` datetime NOT NULL,
  `notified_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `product_id` (`product_id`),
  KEY `variant_id` (`variant_id`),
  KEY `idx_availability_notification_email` (`email`),
  KEY `idx_availability_notification_created_at` (`created_at`),
  CONSTRAINT `availability_notification_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `availability_notification_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `availability_notification_ibfk_3` FOREIGN KEY (`variant_id`) REFERENCES `product_variant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: banner
CREATE TABLE `banner` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `subtitle` varchar(500) DEFAULT NULL,
  `image_url` varchar(500) NOT NULL,
  `link_url` varchar(500) DEFAULT NULL,
  `link_text` varchar(100) DEFAULT NULL,
  `opens_new_tab` tinyint(1) DEFAULT NULL,
  `position` varchar(50) NOT NULL,
  `display_order` int NOT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `created_by` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `idx_banner_created_at` (`created_at`),
  CONSTRAINT `banner_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: carrier_rates
CREATE TABLE `carrier_rates` (
  `id` int NOT NULL AUTO_INCREMENT,
  `carrier_id` int NOT NULL,
  `service_type` varchar(100) NOT NULL,
  `origin_zone` varchar(50) DEFAULT NULL,
  `destination_zone` varchar(50) DEFAULT NULL,
  `weight_from_kg` float NOT NULL,
  `weight_to_kg` float DEFAULT NULL,
  `base_rate` float NOT NULL,
  `per_kg_rate` float NOT NULL,
  `fuel_surcharge_percent` float NOT NULL,
  `estimated_days` int DEFAULT NULL,
  `guaranteed` tinyint(1) NOT NULL,
  `signature_required` tinyint(1) NOT NULL,
  `insurance_included` tinyint(1) NOT NULL,
  `effective_from` date NOT NULL,
  `effective_to` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `carrier_id` (`carrier_id`),
  KEY `idx_carrier_rates_created_at` (`created_at`),
  CONSTRAINT `carrier_rates_ibfk_1` FOREIGN KEY (`carrier_id`) REFERENCES `shipping_carriers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: cart_item
CREATE TABLE `cart_item` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `product_id` int NOT NULL,
  `quantity` int NOT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `product_id` (`product_id`),
  KEY `idx_cart_item_created_at` (`created_at`),
  CONSTRAINT `cart_item_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `cart_item_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: categories
CREATE TABLE `categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text,
  `parent_id` int DEFAULT NULL,
  `level` int NOT NULL DEFAULT '0',
  `sort_order` int NOT NULL DEFAULT '0',
  `image_url` varchar(500) DEFAULT NULL,
  `icon_class` varchar(100) DEFAULT NULL,
  `meta_title` varchar(200) DEFAULT NULL,
  `meta_description` varchar(500) DEFAULT NULL,
  `is_active` tinyint NOT NULL DEFAULT '1',
  `is_featured` tinyint NOT NULL DEFAULT '0',
  `product_count` int NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_categories_parent_id` (`parent_id`),
  KEY `idx_categories_slug` (`slug`),
  KEY `idx_categories_name` (`name`),
  KEY `idx_categories_is_active` (`is_active`),
  KEY `idx_categories_is_featured` (`is_featured`),
  KEY `idx_categories_level` (`level`),
  KEY `idx_categories_sort_order` (`sort_order`),
  KEY `idx_categories_created_at` (`created_at`),
  CONSTRAINT `fk_categories_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: channel_inventory
CREATE TABLE `channel_inventory` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `variant_id` int DEFAULT NULL,
  `channel_id` int NOT NULL,
  `channel_product_id` varchar(100) DEFAULT NULL,
  `channel_sku` varchar(100) DEFAULT NULL,
  `quantity` int NOT NULL,
  `reserved_quantity` int NOT NULL,
  `available_quantity` int NOT NULL,
  `last_synced_at` datetime DEFAULT NULL,
  `sync_status` varchar(20) NOT NULL,
  `sync_error` text,
  `channel_price` float DEFAULT NULL,
  `price_last_updated` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_channel_inventory` (`product_id`,`variant_id`,`channel_id`),
  KEY `variant_id` (`variant_id`),
  KEY `channel_id` (`channel_id`),
  KEY `idx_channel_inventory_created_at` (`created_at`),
  CONSTRAINT `channel_inventory_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `channel_inventory_ibfk_2` FOREIGN KEY (`variant_id`) REFERENCES `product_variant` (`id`),
  CONSTRAINT `channel_inventory_ibfk_3` FOREIGN KEY (`channel_id`) REFERENCES `sales_channel` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: chat_message
CREATE TABLE `chat_message` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` int NOT NULL,
  `sender_type` varchar(20) NOT NULL,
  `sender_id` int DEFAULT NULL,
  `sender_name` varchar(100) DEFAULT NULL,
  `message` text NOT NULL,
  `message_type` varchar(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `read_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `idx_chat_message_created_at` (`created_at`),
  CONSTRAINT `chat_message_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `chat_session` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: chat_session
CREATE TABLE `chat_session` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) NOT NULL,
  `user_id` int DEFAULT NULL,
  `contact_name` varchar(100) DEFAULT NULL,
  `contact_email` varchar(120) DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `assigned_agent_id` int DEFAULT NULL,
  `assigned_agent_name` varchar(100) DEFAULT NULL,
  `started_at` datetime NOT NULL,
  `ended_at` datetime DEFAULT NULL,
  `last_activity` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_chat_session_status` (`status`),
  CONSTRAINT `chat_session_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: community_insight
CREATE TABLE `community_insight` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `summary` varchar(200) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: community_post
CREATE TABLE `community_post` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `content` varchar(500) NOT NULL,
  `post_type` varchar(20) NOT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `video_url` varchar(500) DEFAULT NULL,
  `likes_count` int NOT NULL,
  `comments_count` int NOT NULL,
  `shares_count` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_community_post_created_at` (`created_at`),
  CONSTRAINT `community_post_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: community_stats
CREATE TABLE `community_stats` (
  `id` int NOT NULL AUTO_INCREMENT,
  `total_members` int NOT NULL,
  `posts_today` int NOT NULL,
  `active_users_now` int NOT NULL,
  `total_posts` int NOT NULL,
  `total_hashtags` int NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: content_page
CREATE TABLE `content_page` (
  `id` int NOT NULL AUTO_INCREMENT,
  `slug` varchar(100) NOT NULL,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `meta_title` varchar(200) DEFAULT NULL,
  `meta_description` varchar(500) DEFAULT NULL,
  `meta_keywords` varchar(500) DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT NULL,
  `publish_date` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_content_page_created_at` (`created_at`),
  CONSTRAINT `content_page_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin_user` (`id`),
  CONSTRAINT `content_page_ibfk_2` FOREIGN KEY (`updated_by`) REFERENCES `admin_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: cookie_audit_log
CREATE TABLE `cookie_audit_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `cookie_name` varchar(100) NOT NULL,
  `cookie_category` varchar(50) NOT NULL,
  `action` varchar(20) NOT NULL,
  `cookie_value` text,
  `expiry_date` datetime DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `page_url` varchar(1000) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_cookie_audit_log_created_at` (`created_at`),
  CONSTRAINT `cookie_audit_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: cookie_consent
CREATE TABLE `cookie_consent` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `consent_status` varchar(20) NOT NULL,
  `preferences` json NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `page_url` varchar(1000) DEFAULT NULL,
  `consent_timestamp` datetime NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `withdrawn_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `cookie_consent_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: cookie_consent_history
CREATE TABLE `cookie_consent_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `consent_id` int NOT NULL,
  `action` varchar(50) NOT NULL,
  `old_preferences` json DEFAULT NULL,
  `new_preferences` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `page_url` varchar(1000) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `consent_id` (`consent_id`),
  KEY `idx_cookie_consent_history_created_at` (`created_at`),
  CONSTRAINT `cookie_consent_history_ibfk_1` FOREIGN KEY (`consent_id`) REFERENCES `cookie_consent` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: coupon
CREATE TABLE `coupon` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `discount_type` varchar(20) NOT NULL,
  `discount_value` float NOT NULL,
  `usage_limit` int DEFAULT NULL,
  `usage_limit_per_user` int DEFAULT NULL,
  `used_count` int NOT NULL,
  `minimum_amount` float DEFAULT NULL,
  `maximum_discount` float DEFAULT NULL,
  `valid_from` datetime NOT NULL,
  `valid_until` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `applicable_products` json DEFAULT NULL,
  `applicable_categories` json DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_coupon_created_at` (`created_at`),
  KEY `idx_coupon_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: coupon_usage
CREATE TABLE `coupon_usage` (
  `id` int NOT NULL AUTO_INCREMENT,
  `coupon_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `order_id` int DEFAULT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `discount_amount` float NOT NULL,
  `used_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `coupon_id` (`coupon_id`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`),
  CONSTRAINT `coupon_usage_ibfk_1` FOREIGN KEY (`coupon_id`) REFERENCES `coupon` (`id`),
  CONSTRAINT `coupon_usage_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `coupon_usage_ibfk_3` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: data_export_request
CREATE TABLE `data_export_request` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `request_type` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `export_format` varchar(20) NOT NULL,
  `export_file_path` varchar(500) DEFAULT NULL,
  `export_file_size` int DEFAULT NULL,
  `requested_at` datetime NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `error_message` text,
  `retry_count` int NOT NULL,
  `image_format` varchar(10) DEFAULT NULL,
  `results_count` int NOT NULL,
  `similarity_threshold` float DEFAULT NULL,
  `processing_time` float DEFAULT NULL,
  `clicked_results` json DEFAULT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_data_export_request_status` (`status`),
  KEY `idx_data_export_request_created_at` (`created_at`),
  CONSTRAINT `data_export_request_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: email_notification
CREATE TABLE `email_notification` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email_type` varchar(50) NOT NULL,
  `recipient_email` varchar(120) NOT NULL,
  `subject` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `order_id` int DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `is_sent` tinyint(1) NOT NULL,
  `sent_at` datetime DEFAULT NULL,
  `failed_attempts` int NOT NULL,
  `last_error` text,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_email_notification_created_at` (`created_at`),
  CONSTRAINT `email_notification_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `email_notification_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: fulfillment_rules
CREATE TABLE `fulfillment_rules` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `conditions` json DEFAULT NULL,
  `actions` json DEFAULT NULL,
  `priority` int NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `execution_count` int NOT NULL,
  `last_executed` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_fulfillment_rules_created_at` (`created_at`),
  KEY `idx_fulfillment_rules_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: guest_session
CREATE TABLE `guest_session` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) NOT NULL,
  `email` varchar(120) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `last_activity` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `idx_guest_session_email` (`email`),
  KEY `idx_guest_session_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: hashtag
CREATE TABLE `hashtag` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tag` varchar(50) NOT NULL,
  `usage_count` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_hashtag_tag` (`tag`),
  KEY `idx_hashtag_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: inventory_conflict
CREATE TABLE `inventory_conflict` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `variant_id` int DEFAULT NULL,
  `conflict_type` varchar(50) NOT NULL,
  `description` text NOT NULL,
  `master_quantity` int NOT NULL,
  `conflicting_data` json NOT NULL,
  `status` varchar(20) NOT NULL,
  `resolution_strategy` varchar(50) DEFAULT NULL,
  `resolved_quantity` int DEFAULT NULL,
  `resolved_by` int DEFAULT NULL,
  `resolved_at` datetime DEFAULT NULL,
  `resolution_notes` text,
  `priority` varchar(10) NOT NULL,
  `impact_score` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `variant_id` (`variant_id`),
  KEY `resolved_by` (`resolved_by`),
  KEY `idx_inventory_conflict_status` (`status`),
  KEY `idx_inventory_conflict_created_at` (`created_at`),
  CONSTRAINT `inventory_conflict_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `inventory_conflict_ibfk_2` FOREIGN KEY (`variant_id`) REFERENCES `product_variant` (`id`),
  CONSTRAINT `inventory_conflict_ibfk_3` FOREIGN KEY (`resolved_by`) REFERENCES `admin_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: inventory_log
CREATE TABLE `inventory_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `variant_id` int DEFAULT NULL,
  `change_type` varchar(20) NOT NULL,
  `quantity_change` int NOT NULL,
  `previous_quantity` int NOT NULL,
  `new_quantity` int NOT NULL,
  `reason` varchar(200) DEFAULT NULL,
  `reference_id` int DEFAULT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `admin_id` int DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `variant_id` (`variant_id`),
  KEY `admin_id` (`admin_id`),
  KEY `idx_inventory_log_created_at` (`created_at`),
  CONSTRAINT `inventory_log_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `inventory_log_ibfk_2` FOREIGN KEY (`variant_id`) REFERENCES `product_variant` (`id`),
  CONSTRAINT `inventory_log_ibfk_3` FOREIGN KEY (`admin_id`) REFERENCES `admin_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: inventory_sync_log
CREATE TABLE `inventory_sync_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `variant_id` int DEFAULT NULL,
  `channel_id` int NOT NULL,
  `sync_type` varchar(20) NOT NULL,
  `operation` varchar(20) NOT NULL,
  `old_quantity` int DEFAULT NULL,
  `new_quantity` int NOT NULL,
  `quantity_change` int NOT NULL,
  `status` varchar(20) NOT NULL,
  `error_message` text,
  `retry_count` int NOT NULL,
  `request_data` json DEFAULT NULL,
  `response_data` json DEFAULT NULL,
  `started_at` datetime NOT NULL,
  `completed_at` datetime DEFAULT NULL,
  `duration_ms` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `variant_id` (`variant_id`),
  KEY `channel_id` (`channel_id`),
  KEY `idx_inventory_sync_log_status` (`status`),
  CONSTRAINT `inventory_sync_log_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `inventory_sync_log_ibfk_2` FOREIGN KEY (`variant_id`) REFERENCES `product_variant` (`id`),
  CONSTRAINT `inventory_sync_log_ibfk_3` FOREIGN KEY (`channel_id`) REFERENCES `sales_channel` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: invoice
CREATE TABLE `invoice` (
  `id` int NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(50) NOT NULL,
  `order_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `customer_name` varchar(200) NOT NULL,
  `customer_email` varchar(120) NOT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `billing_address` json NOT NULL,
  `shipping_address` json NOT NULL,
  `subtotal` float NOT NULL,
  `tax_amount` float NOT NULL,
  `shipping_amount` float NOT NULL,
  `discount_amount` float NOT NULL,
  `total_amount` float NOT NULL,
  `tax_breakdown` json DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `payment_status` varchar(20) NOT NULL,
  `payment_due_date` datetime DEFAULT NULL,
  `payment_terms` varchar(100) DEFAULT NULL,
  `pdf_file_path` varchar(500) DEFAULT NULL,
  `pdf_generated_at` datetime DEFAULT NULL,
  `email_sent_at` datetime DEFAULT NULL,
  `email_opened_at` datetime DEFAULT NULL,
  `notes` text,
  `reference_number` varchar(100) DEFAULT NULL,
  `invoice_date` datetime NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invoice_number` (`invoice_number`),
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_invoice_status` (`status`),
  KEY `idx_invoice_created_at` (`created_at`),
  CONSTRAINT `invoice_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `invoice_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: newsletter_subscription
CREATE TABLE `newsletter_subscription` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(120) NOT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `subscription_source` varchar(50) DEFAULT NULL,
  `preferences` json DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `unsubscribed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_newsletter_subscription_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: o_auth_provider
CREATE TABLE `o_auth_provider` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `client_id` varchar(255) NOT NULL,
  `client_secret` varchar(255) NOT NULL,
  `authorization_url` varchar(500) NOT NULL,
  `token_url` varchar(500) NOT NULL,
  `user_info_url` varchar(500) NOT NULL,
  `scope` varchar(500) NOT NULL,
  `button_color` varchar(20) DEFAULT NULL,
  `icon_url` varchar(500) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_o_auth_provider_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: order
CREATE TABLE `order` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `order_number` varchar(50) NOT NULL,
  `is_guest_order` tinyint(1) DEFAULT NULL,
  `guest_email` varchar(120) DEFAULT NULL,
  `guest_phone` varchar(20) DEFAULT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `subtotal` float NOT NULL,
  `tax_amount` float NOT NULL,
  `shipping_amount` float NOT NULL,
  `discount_amount` float NOT NULL,
  `total_amount` float NOT NULL,
  `shipping_address` json NOT NULL,
  `billing_address` json DEFAULT NULL,
  `payment_method` varchar(50) NOT NULL,
  `payment_status` varchar(20) NOT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `tracking_number` varchar(100) DEFAULT NULL,
  `estimated_delivery` date DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `shipped_at` datetime DEFAULT NULL,
  `delivered_at` datetime DEFAULT NULL,
  `order_notes` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `order_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: order_item
CREATE TABLE `order_item` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL,
  `product_id` int NOT NULL,
  `quantity` int NOT NULL,
  `unit_price` float NOT NULL,
  `total_price` float NOT NULL,
  `product_name` varchar(255) NOT NULL,
  `product_image` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `order_item_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `order_item_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: orders
CREATE TABLE `orders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `order_number` varchar(50) NOT NULL,
  `seller_id` int DEFAULT NULL,
  `is_guest_order` tinyint(1) DEFAULT NULL,
  `guest_email` varchar(120) DEFAULT NULL,
  `guest_phone` varchar(20) DEFAULT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `subtotal` float NOT NULL,
  `tax_amount` float NOT NULL,
  `shipping_amount` float NOT NULL,
  `discount_amount` float NOT NULL,
  `total_amount` float NOT NULL,
  `shipping_address` json NOT NULL,
  `billing_address` json DEFAULT NULL,
  `payment_method` varchar(50) NOT NULL,
  `payment_status` varchar(20) NOT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `tracking_number` varchar(100) DEFAULT NULL,
  `estimated_delivery` date DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `shipped_at` datetime DEFAULT NULL,
  `delivered_at` datetime DEFAULT NULL,
  `order_notes` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `user_id` (`user_id`),
  KEY `seller_id` (`seller_id`),
  KEY `idx_orders_status` (`status`),
  KEY `idx_orders_created_at` (`created_at`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`seller_id`) REFERENCES `sellers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: payment_gateway
CREATE TABLE `payment_gateway` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `is_test_mode` tinyint(1) DEFAULT NULL,
  `supported_currencies` json NOT NULL,
  `supported_payment_methods` json NOT NULL,
  `api_key` varchar(500) DEFAULT NULL,
  `api_secret` varchar(500) DEFAULT NULL,
  `webhook_secret` varchar(500) DEFAULT NULL,
  `processing_fee_percentage` float DEFAULT NULL,
  `min_amount` float DEFAULT NULL,
  `max_amount` float DEFAULT NULL,
  `logo_url` varchar(500) DEFAULT NULL,
  `description` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_payment_gateway_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: payment_method
CREATE TABLE `payment_method` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `payment_type` varchar(20) NOT NULL,
  `is_default` tinyint(1) DEFAULT NULL,
  `card_last_four` varchar(4) DEFAULT NULL,
  `card_brand` varchar(20) DEFAULT NULL,
  `card_expiry_month` int DEFAULT NULL,
  `card_expiry_year` int DEFAULT NULL,
  `upi_id` varchar(100) DEFAULT NULL,
  `wallet_type` varchar(50) DEFAULT NULL,
  `nickname` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_payment_method_created_at` (`created_at`),
  CONSTRAINT `payment_method_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: payment_transaction
CREATE TABLE `payment_transaction` (
  `id` int NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(100) NOT NULL,
  `order_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `gateway_id` int NOT NULL,
  `gateway_transaction_id` varchar(200) DEFAULT NULL,
  `gateway_payment_intent_id` varchar(200) DEFAULT NULL,
  `amount` float NOT NULL,
  `currency` varchar(3) NOT NULL,
  `payment_method_type` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `failure_reason` varchar(500) DEFAULT NULL,
  `payment_method_details` json DEFAULT NULL,
  `gateway_response` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `risk_score` float DEFAULT NULL,
  `initiated_at` datetime NOT NULL,
  `completed_at` datetime DEFAULT NULL,
  `failed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `gateway_id` (`gateway_id`),
  KEY `idx_payment_transaction_status` (`status`),
  CONSTRAINT `payment_transaction_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `payment_transaction_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `payment_transaction_ibfk_3` FOREIGN KEY (`gateway_id`) REFERENCES `payment_gateway` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: post_comment
CREATE TABLE `post_comment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `post_id` int NOT NULL,
  `user_id` int NOT NULL,
  `content` varchar(300) NOT NULL,
  `parent_comment_id` int DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `post_id` (`post_id`),
  KEY `user_id` (`user_id`),
  KEY `parent_comment_id` (`parent_comment_id`),
  KEY `idx_post_comment_created_at` (`created_at`),
  CONSTRAINT `post_comment_ibfk_1` FOREIGN KEY (`post_id`) REFERENCES `community_post` (`id`),
  CONSTRAINT `post_comment_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `post_comment_ibfk_3` FOREIGN KEY (`parent_comment_id`) REFERENCES `post_comment` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: post_hashtag
CREATE TABLE `post_hashtag` (
  `id` int NOT NULL AUTO_INCREMENT,
  `post_id` int NOT NULL,
  `hashtag_id` int NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_post_hashtag` (`post_id`,`hashtag_id`),
  KEY `hashtag_id` (`hashtag_id`),
  KEY `idx_post_hashtag_created_at` (`created_at`),
  CONSTRAINT `post_hashtag_ibfk_1` FOREIGN KEY (`post_id`) REFERENCES `community_post` (`id`),
  CONSTRAINT `post_hashtag_ibfk_2` FOREIGN KEY (`hashtag_id`) REFERENCES `hashtag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: post_like
CREATE TABLE `post_like` (
  `id` int NOT NULL AUTO_INCREMENT,
  `post_id` int NOT NULL,
  `user_id` int NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_post_like` (`post_id`,`user_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_post_like_created_at` (`created_at`),
  CONSTRAINT `post_like_ibfk_1` FOREIGN KEY (`post_id`) REFERENCES `community_post` (`id`),
  CONSTRAINT `post_like_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: price_history
CREATE TABLE `price_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `price` float NOT NULL,
  `recorded_date` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `price_history_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: product
CREATE TABLE `product` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `price` float NOT NULL,
  `image` varchar(200) NOT NULL,
  `sustainability_score` int NOT NULL,
  `stock_quantity` int NOT NULL,
  `description` text,
  `category` varchar(50) DEFAULT NULL,
  `brand` varchar(50) DEFAULT NULL,
  `sku` varchar(100) DEFAULT NULL,
  `weight` decimal(10,2) DEFAULT NULL,
  `dimensions` varchar(100) DEFAULT NULL,
  `material` varchar(100) DEFAULT NULL,
  `care_instructions` text,
  `average_rating` decimal(3,2) DEFAULT '0.00',
  `total_reviews` int DEFAULT '0',
  `low_stock_threshold` int NOT NULL DEFAULT '10',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product_name` (`name`),
  KEY `idx_product_category` (`category`),
  KEY `idx_product_brand` (`brand`),
  KEY `idx_product_material` (`material`),
  KEY `idx_product_price` (`price`),
  KEY `idx_product_sustainability` (`sustainability_score`),
  KEY `idx_product_stock` (`stock_quantity`),
  KEY `idx_product_rating` (`average_rating`),
  KEY `idx_product_category_price` (`category`,`price`),
  KEY `idx_product_brand_price` (`brand`,`price`),
  KEY `idx_product_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: product_comparison
CREATE TABLE `product_comparison` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `product_ids` text NOT NULL,
  `comparison_name` varchar(200) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_product_comparison_created_at` (`created_at`),
  CONSTRAINT `product_comparison_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: product_image
CREATE TABLE `product_image` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `image_url` varchar(500) NOT NULL,
  `alt_text` varchar(200) DEFAULT NULL,
  `is_primary` tinyint(1) NOT NULL,
  `display_order` int NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `idx_product_image_created_at` (`created_at`),
  CONSTRAINT `product_image_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: product_review
CREATE TABLE `product_review` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `user_id` int NOT NULL,
  `rating` int NOT NULL,
  `title` varchar(200) DEFAULT NULL,
  `comment` text,
  `verified_purchase` tinyint(1) NOT NULL,
  `helpful_count` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_product_review_created_at` (`created_at`),
  CONSTRAINT `product_review_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `product_review_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: product_variant
CREATE TABLE `product_variant` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `variant_type` varchar(50) NOT NULL,
  `variant_value` varchar(100) NOT NULL,
  `price_adjustment` float NOT NULL,
  `stock_quantity` int NOT NULL,
  `sku_suffix` varchar(20) DEFAULT NULL,
  `is_available` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `product_variant_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: products
CREATE TABLE `products` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `price` float NOT NULL,
  `image` varchar(200) NOT NULL,
  `sustainability_score` int NOT NULL,
  `stock_quantity` int NOT NULL,
  `seller_id` int DEFAULT NULL,
  `description` text,
  `category` varchar(50) DEFAULT NULL,
  `brand` varchar(50) DEFAULT NULL,
  `sku` varchar(50) DEFAULT NULL,
  `weight` float DEFAULT NULL,
  `dimensions` varchar(100) DEFAULT NULL,
  `material` varchar(100) DEFAULT NULL,
  `care_instructions` text,
  `average_rating` float DEFAULT NULL,
  `total_reviews` int DEFAULT NULL,
  `low_stock_threshold` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sku` (`sku`),
  KEY `seller_id` (`seller_id`),
  KEY `ix_products_name` (`name`),
  KEY `ix_products_price` (`price`),
  KEY `ix_products_average_rating` (`average_rating`),
  KEY `ix_products_category` (`category`),
  KEY `ix_products_brand` (`brand`),
  KEY `ix_products_sustainability_score` (`sustainability_score`),
  KEY `ix_products_stock_quantity` (`stock_quantity`),
  KEY `ix_products_material` (`material`),
  KEY `idx_products_created_at` (`created_at`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`seller_id`) REFERENCES `sellers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: recently_viewed
CREATE TABLE `recently_viewed` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `product_id` int NOT NULL,
  `viewed_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_product_view` (`user_id`,`product_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `recently_viewed_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `recently_viewed_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: refund
CREATE TABLE `refund` (
  `id` int NOT NULL AUTO_INCREMENT,
  `refund_id` varchar(50) NOT NULL,
  `order_id` int NOT NULL,
  `transaction_id` int DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `refund_type` varchar(20) NOT NULL,
  `refund_reason` varchar(50) NOT NULL,
  `refund_amount` float NOT NULL,
  `original_amount` float NOT NULL,
  `refunded_items` json DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `processing_method` varchar(50) NOT NULL,
  `gateway_id` int DEFAULT NULL,
  `gateway_refund_id` varchar(200) DEFAULT NULL,
  `gateway_response` json DEFAULT NULL,
  `customer_notes` text,
  `admin_notes` text,
  `requested_by` varchar(100) DEFAULT NULL,
  `approved_by` varchar(100) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `requested_at` datetime NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `refund_id` (`refund_id`),
  KEY `order_id` (`order_id`),
  KEY `transaction_id` (`transaction_id`),
  KEY `user_id` (`user_id`),
  KEY `gateway_id` (`gateway_id`),
  KEY `idx_refund_status` (`status`),
  CONSTRAINT `refund_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `refund_ibfk_2` FOREIGN KEY (`transaction_id`) REFERENCES `payment_transaction` (`id`),
  CONSTRAINT `refund_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `refund_ibfk_4` FOREIGN KEY (`gateway_id`) REFERENCES `payment_gateway` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: return_shipment
CREATE TABLE `return_shipment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `rma_request_id` int NOT NULL,
  `tracking_number` varchar(100) NOT NULL,
  `carrier` varchar(50) NOT NULL,
  `service_type` varchar(50) DEFAULT NULL,
  `origin_address` json NOT NULL,
  `destination_address` json NOT NULL,
  `weight_kg` float DEFAULT NULL,
  `dimensions_cm` json DEFAULT NULL,
  `declared_value` float DEFAULT NULL,
  `shipping_cost` float NOT NULL,
  `label_url` varchar(500) DEFAULT NULL,
  `label_format` varchar(20) DEFAULT NULL,
  `status` varchar(30) NOT NULL,
  `shipped_at` datetime DEFAULT NULL,
  `delivered_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tracking_number` (`tracking_number`),
  KEY `rma_request_id` (`rma_request_id`),
  KEY `idx_return_shipment_status` (`status`),
  KEY `idx_return_shipment_created_at` (`created_at`),
  CONSTRAINT `return_shipment_ibfk_1` FOREIGN KEY (`rma_request_id`) REFERENCES `rma_request` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: rma_approval
CREATE TABLE `rma_approval` (
  `id` int NOT NULL AUTO_INCREMENT,
  `rma_request_id` int NOT NULL,
  `approval_level` int NOT NULL,
  `approver_type` varchar(20) NOT NULL,
  `approver_id` varchar(100) NOT NULL,
  `approver_name` varchar(100) NOT NULL,
  `decision` varchar(20) NOT NULL,
  `decision_reason` text,
  `conditions` json DEFAULT NULL,
  `assigned_at` datetime NOT NULL,
  `decided_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `rma_request_id` (`rma_request_id`),
  CONSTRAINT `rma_approval_ibfk_1` FOREIGN KEY (`rma_request_id`) REFERENCES `rma_request` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: rma_configuration
CREATE TABLE `rma_configuration` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL,
  `config_value` text NOT NULL,
  `data_type` varchar(20) NOT NULL,
  `description` text,
  `category` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `updated_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`),
  KEY `idx_rma_configuration_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: rma_document
CREATE TABLE `rma_document` (
  `id` int NOT NULL AUTO_INCREMENT,
  `rma_request_id` int NOT NULL,
  `document_type` varchar(30) NOT NULL,
  `document_name` varchar(200) NOT NULL,
  `document_url` varchar(500) NOT NULL,
  `file_size` int DEFAULT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `uploaded_by` varchar(100) DEFAULT NULL,
  `uploaded_at` datetime NOT NULL,
  `description` text,
  `is_public` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `rma_request_id` (`rma_request_id`),
  CONSTRAINT `rma_document_ibfk_1` FOREIGN KEY (`rma_request_id`) REFERENCES `rma_request` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: rma_item
CREATE TABLE `rma_item` (
  `id` int NOT NULL AUTO_INCREMENT,
  `rma_request_id` int NOT NULL,
  `order_item_id` int NOT NULL,
  `product_id` int NOT NULL,
  `quantity` int NOT NULL,
  `unit_price` float NOT NULL,
  `total_price` float NOT NULL,
  `return_reason` varchar(30) NOT NULL,
  `condition_notes` text,
  `photos` json DEFAULT NULL,
  `exchange_product_id` int DEFAULT NULL,
  `exchange_variant_id` int DEFAULT NULL,
  `exchange_quantity` int DEFAULT NULL,
  `exchange_unit_price` float DEFAULT NULL,
  `item_status` varchar(30) NOT NULL,
  `inspection_result` varchar(20) DEFAULT NULL,
  `inspection_notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `rma_request_id` (`rma_request_id`),
  KEY `order_item_id` (`order_item_id`),
  KEY `product_id` (`product_id`),
  KEY `exchange_product_id` (`exchange_product_id`),
  KEY `idx_rma_item_created_at` (`created_at`),
  CONSTRAINT `rma_item_ibfk_1` FOREIGN KEY (`rma_request_id`) REFERENCES `rma_request` (`id`),
  CONSTRAINT `rma_item_ibfk_2` FOREIGN KEY (`order_item_id`) REFERENCES `order_item` (`id`),
  CONSTRAINT `rma_item_ibfk_3` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `rma_item_ibfk_4` FOREIGN KEY (`exchange_product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: rma_request
CREATE TABLE `rma_request` (
  `id` int NOT NULL AUTO_INCREMENT,
  `rma_number` varchar(50) NOT NULL,
  `order_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `rma_type` varchar(30) NOT NULL,
  `status` varchar(30) NOT NULL,
  `customer_email` varchar(120) NOT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `customer_notes` text,
  `total_refund_amount` float NOT NULL,
  `restocking_fee` float NOT NULL,
  `return_shipping_cost` float NOT NULL,
  `requires_approval` tinyint(1) NOT NULL,
  `approved_by` varchar(100) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `rejection_reason` text,
  `return_label_url` varchar(500) DEFAULT NULL,
  `return_tracking_number` varchar(100) DEFAULT NULL,
  `return_carrier` varchar(50) DEFAULT NULL,
  `return_shipped_at` datetime DEFAULT NULL,
  `return_received_at` datetime DEFAULT NULL,
  `inspection_result` varchar(20) DEFAULT NULL,
  `inspection_notes` text,
  `inspected_by` varchar(100) DEFAULT NULL,
  `inspected_at` datetime DEFAULT NULL,
  `refund_method` varchar(30) DEFAULT NULL,
  `refund_reference` varchar(100) DEFAULT NULL,
  `refunded_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `deadline` datetime DEFAULT NULL,
  `admin_notes` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `rma_number` (`rma_number`),
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_rma_request_status` (`status`),
  KEY `idx_rma_request_created_at` (`created_at`),
  CONSTRAINT `rma_request_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `rma_request_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: rma_rule
CREATE TABLE `rma_rule` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `rule_type` varchar(30) NOT NULL,
  `condition_expression` text NOT NULL,
  `action` varchar(50) NOT NULL,
  `priority` int NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `parameters` json DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_rma_rule_created_at` (`created_at`),
  KEY `idx_rma_rule_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: rma_stats
CREATE TABLE `rma_stats` (
  `id` int NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `period_type` varchar(20) NOT NULL,
  `total_requests` int NOT NULL,
  `approved_requests` int NOT NULL,
  `rejected_requests` int NOT NULL,
  `completed_requests` int NOT NULL,
  `total_refund_amount` float NOT NULL,
  `total_restocking_fees` float NOT NULL,
  `total_return_shipping_cost` float NOT NULL,
  `avg_approval_time` float DEFAULT NULL,
  `avg_processing_time` float DEFAULT NULL,
  `avg_refund_time` float DEFAULT NULL,
  `reason_breakdown` json DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_rma_stats_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: rma_timeline
CREATE TABLE `rma_timeline` (
  `id` int NOT NULL AUTO_INCREMENT,
  `rma_request_id` int NOT NULL,
  `event_type` varchar(50) NOT NULL,
  `event_description` text NOT NULL,
  `old_status` varchar(30) DEFAULT NULL,
  `new_status` varchar(30) DEFAULT NULL,
  `actor_type` varchar(20) NOT NULL,
  `actor_id` varchar(100) DEFAULT NULL,
  `actor_name` varchar(100) DEFAULT NULL,
  `event_data` json DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `rma_request_id` (`rma_request_id`),
  KEY `idx_rma_timeline_created_at` (`created_at`),
  CONSTRAINT `rma_timeline_ibfk_1` FOREIGN KEY (`rma_request_id`) REFERENCES `rma_request` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: sales
CREATE TABLE `sales` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `quantity_sold` int NOT NULL,
  `sale_date` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `sales_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: sales_channel
CREATE TABLE `sales_channel` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `channel_type` varchar(50) NOT NULL,
  `api_endpoint` varchar(500) DEFAULT NULL,
  `api_key` varchar(200) DEFAULT NULL,
  `api_secret` varchar(200) DEFAULT NULL,
  `webhook_url` varchar(500) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  `sync_enabled` tinyint(1) NOT NULL,
  `priority` int NOT NULL,
  `sync_frequency` int NOT NULL,
  `last_sync_at` datetime DEFAULT NULL,
  `next_sync_at` datetime DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `last_error` text,
  `error_count` int NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_sales_channel_status` (`status`),
  KEY `idx_sales_channel_created_at` (`created_at`),
  KEY `idx_sales_channel_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: saved_cart
CREATE TABLE `saved_cart` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `cart_data` json NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_saved_cart_created_at` (`created_at`),
  KEY `idx_saved_cart_name` (`name`),
  CONSTRAINT `saved_cart_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: search_analytics
CREATE TABLE `search_analytics` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `search_query` varchar(500) NOT NULL,
  `search_type` varchar(50) NOT NULL DEFAULT 'text',
  `results_count` int NOT NULL DEFAULT '0',
  `filters_applied` json DEFAULT NULL,
  `clicked_results` json DEFAULT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` varchar(500) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `response_time_ms` float DEFAULT NULL,
  `elasticsearch_time_ms` float DEFAULT NULL,
  `conversion_events` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_search_analytics_user_id` (`user_id`),
  KEY `idx_search_analytics_session_id` (`session_id`),
  KEY `idx_search_analytics_search_query` (`search_query`(255)),
  KEY `idx_search_analytics_search_type` (`search_type`),
  KEY `idx_search_analytics_created_at` (`created_at`),
  KEY `idx_search_analytics_guest_session` (`guest_session_id`),
  CONSTRAINT `fk_search_analytics_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: search_clicks
CREATE TABLE `search_clicks` (
  `id` varchar(36) NOT NULL,
  `search_analytics_id` int NOT NULL,
  `product_id` int NOT NULL,
  `position` int NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `user_id` int DEFAULT NULL,
  `session_id` varchar(64) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_search_clicks_search_analytics` (`search_analytics_id`),
  KEY `idx_search_clicks_product_id` (`product_id`),
  KEY `idx_search_clicks_user_id` (`user_id`),
  KEY `idx_search_clicks_timestamp` (`timestamp`),
  CONSTRAINT `fk_search_clicks_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_search_clicks_search_analytics` FOREIGN KEY (`search_analytics_id`) REFERENCES `search_analytics` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_search_clicks_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: search_conversions
CREATE TABLE `search_conversions` (
  `id` varchar(36) NOT NULL,
  `search_analytics_id` int NOT NULL,
  `product_id` int NOT NULL,
  `conversion_type` varchar(20) NOT NULL,
  `conversion_value` float DEFAULT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `user_id` int DEFAULT NULL,
  `session_id` varchar(64) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_search_conversions_search_analytics` (`search_analytics_id`),
  KEY `idx_search_conversions_product_id` (`product_id`),
  KEY `idx_search_conversions_user_id` (`user_id`),
  KEY `idx_search_conversions_type` (`conversion_type`),
  KEY `idx_search_conversions_timestamp` (`timestamp`),
  CONSTRAINT `fk_search_conversions_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_search_conversions_search_analytics` FOREIGN KEY (`search_analytics_id`) REFERENCES `search_analytics` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_search_conversions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: seller
CREATE TABLE `seller` (
  `id` int NOT NULL AUTO_INCREMENT,
  `business_name` varchar(200) NOT NULL,
  `contact_person` varchar(100) NOT NULL,
  `email` varchar(120) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `business_type` varchar(50) NOT NULL,
  `category` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `address` text NOT NULL,
  `gst_number` varchar(15) NOT NULL,
  `pan_number` varchar(10) NOT NULL,
  `bank_account` varchar(20) DEFAULT NULL,
  `ifsc_code` varchar(11) DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `approved_at` datetime DEFAULT NULL,
  `approved_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `gst_number` (`gst_number`),
  KEY `approved_by` (`approved_by`),
  KEY `idx_seller_status` (`status`),
  KEY `idx_seller_created_at` (`created_at`),
  CONSTRAINT `seller_ibfk_1` FOREIGN KEY (`approved_by`) REFERENCES `admin_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: seller_commission
CREATE TABLE `seller_commission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `seller_id` int NOT NULL,
  `order_id` int NOT NULL,
  `product_id` int NOT NULL,
  `order_amount` float NOT NULL,
  `commission_rate` float NOT NULL,
  `commission_amount` float NOT NULL,
  `seller_earnings` float NOT NULL,
  `status` varchar(20) DEFAULT NULL,
  `payout_id` int DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `seller_id` (`seller_id`),
  KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`),
  KEY `payout_id` (`payout_id`),
  KEY `idx_seller_commission_status` (`status`),
  KEY `idx_seller_commission_created_at` (`created_at`),
  CONSTRAINT `seller_commission_ibfk_1` FOREIGN KEY (`seller_id`) REFERENCES `sellers` (`id`),
  CONSTRAINT `seller_commission_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `seller_commission_ibfk_3` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `seller_commission_ibfk_4` FOREIGN KEY (`payout_id`) REFERENCES `seller_payout` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: seller_payout
CREATE TABLE `seller_payout` (
  `id` int NOT NULL AUTO_INCREMENT,
  `seller_id` int NOT NULL,
  `amount` float NOT NULL,
  `currency` varchar(3) DEFAULT NULL,
  `payout_method` varchar(50) DEFAULT NULL,
  `bank_account` varchar(20) DEFAULT NULL,
  `ifsc_code` varchar(11) DEFAULT NULL,
  `bank_holder_name` varchar(100) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `processed_by` int DEFAULT NULL,
  `processed_at` datetime DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `seller_id` (`seller_id`),
  KEY `processed_by` (`processed_by`),
  KEY `idx_seller_payout_status` (`status`),
  KEY `idx_seller_payout_created_at` (`created_at`),
  CONSTRAINT `seller_payout_ibfk_1` FOREIGN KEY (`seller_id`) REFERENCES `sellers` (`id`),
  CONSTRAINT `seller_payout_ibfk_2` FOREIGN KEY (`processed_by`) REFERENCES `admin_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: seller_store
CREATE TABLE `seller_store` (
  `id` int NOT NULL AUTO_INCREMENT,
  `seller_id` int NOT NULL,
  `store_name` varchar(200) NOT NULL,
  `store_slug` varchar(100) NOT NULL,
  `store_description` text,
  `store_logo` varchar(500) DEFAULT NULL,
  `store_banner` varchar(500) DEFAULT NULL,
  `store_email` varchar(120) DEFAULT NULL,
  `store_phone` varchar(20) DEFAULT NULL,
  `store_address` text,
  `is_active` tinyint(1) DEFAULT NULL,
  `featured` tinyint(1) DEFAULT NULL,
  `rating` float DEFAULT NULL,
  `total_reviews` int DEFAULT NULL,
  `meta_title` varchar(200) DEFAULT NULL,
  `meta_description` text,
  `meta_keywords` varchar(500) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `store_slug` (`store_slug`),
  KEY `seller_id` (`seller_id`),
  KEY `idx_seller_store_created_at` (`created_at`),
  CONSTRAINT `seller_store_ibfk_1` FOREIGN KEY (`seller_id`) REFERENCES `sellers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: sellers
CREATE TABLE `sellers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `business_name` varchar(200) NOT NULL,
  `contact_person` varchar(100) NOT NULL,
  `email` varchar(120) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `password_hash` varchar(255) DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT NULL,
  `verification_token` varchar(255) DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `business_type` varchar(50) NOT NULL,
  `category` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `address` text NOT NULL,
  `gst_number` varchar(15) NOT NULL,
  `pan_number` varchar(10) NOT NULL,
  `bank_account` varchar(20) DEFAULT NULL,
  `ifsc_code` varchar(11) DEFAULT NULL,
  `bank_holder_name` varchar(100) DEFAULT NULL,
  `store_name` varchar(200) DEFAULT NULL,
  `store_description` text,
  `store_logo` varchar(500) DEFAULT NULL,
  `store_banner` varchar(500) DEFAULT NULL,
  `store_slug` varchar(100) DEFAULT NULL,
  `return_policy` text,
  `shipping_policy` text,
  `terms_conditions` text,
  `min_order_amount` float DEFAULT NULL,
  `free_shipping_threshold` float DEFAULT NULL,
  `processing_time` varchar(50) DEFAULT NULL,
  `commission_rate` float DEFAULT NULL,
  `total_earnings` float DEFAULT NULL,
  `pending_earnings` float DEFAULT NULL,
  `paid_earnings` float DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `approved_at` datetime DEFAULT NULL,
  `approved_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `gst_number` (`gst_number`),
  UNIQUE KEY `store_slug` (`store_slug`),
  KEY `approved_by` (`approved_by`),
  KEY `idx_sellers_status` (`status`),
  KEY `idx_sellers_created_at` (`created_at`),
  CONSTRAINT `sellers_ibfk_1` FOREIGN KEY (`approved_by`) REFERENCES `admin_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: shipments
CREATE TABLE `shipments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL,
  `carrier_id` int NOT NULL,
  `tracking_number` varchar(100) DEFAULT NULL,
  `service_type` varchar(100) DEFAULT NULL,
  `status` enum('pending','label_created','picked_up','in_transit','out_for_delivery','delivered','exception','returned','cancelled') NOT NULL,
  `weight_kg` float DEFAULT NULL,
  `dimensions_cm` json DEFAULT NULL,
  `declared_value` float DEFAULT NULL,
  `insurance_value` float DEFAULT NULL,
  `origin_address` json DEFAULT NULL,
  `destination_address` json DEFAULT NULL,
  `shipping_cost` float DEFAULT NULL,
  `estimated_delivery_date` date DEFAULT NULL,
  `actual_delivery_date` datetime DEFAULT NULL,
  `label_url` varchar(500) DEFAULT NULL,
  `label_format` varchar(20) DEFAULT NULL,
  `pickup_scheduled` tinyint(1) NOT NULL,
  `pickup_date` date DEFAULT NULL,
  `pickup_time_window` varchar(50) DEFAULT NULL,
  `carrier_reference` varchar(100) DEFAULT NULL,
  `special_instructions` text,
  `signature_required` tinyint(1) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tracking_number` (`tracking_number`),
  KEY `order_id` (`order_id`),
  KEY `carrier_id` (`carrier_id`),
  KEY `idx_shipments_status` (`status`),
  KEY `idx_shipments_created_at` (`created_at`),
  CONSTRAINT `shipments_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `shipments_ibfk_2` FOREIGN KEY (`carrier_id`) REFERENCES `shipping_carriers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: shipping_carriers
CREATE TABLE `shipping_carriers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `code` varchar(20) NOT NULL,
  `api_endpoint` varchar(255) DEFAULT NULL,
  `api_key_encrypted` text,
  `is_active` tinyint(1) NOT NULL,
  `supported_services` json DEFAULT NULL,
  `rate_calculation_method` varchar(50) DEFAULT NULL,
  `max_weight_kg` float DEFAULT NULL,
  `max_dimensions_cm` json DEFAULT NULL,
  `domestic_coverage` json DEFAULT NULL,
  `international_coverage` json DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_shipping_carriers_created_at` (`created_at`),
  KEY `idx_shipping_carriers_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: shipping_method
CREATE TABLE `shipping_method` (
  `id` int NOT NULL AUTO_INCREMENT,
  `zone_id` int NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `base_cost` float NOT NULL,
  `cost_per_kg` float NOT NULL,
  `free_shipping_threshold` float DEFAULT NULL,
  `min_delivery_days` int NOT NULL,
  `max_delivery_days` int NOT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `zone_id` (`zone_id`),
  KEY `idx_shipping_method_name` (`name`),
  CONSTRAINT `shipping_method_ibfk_1` FOREIGN KEY (`zone_id`) REFERENCES `shipping_zone` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: shipping_zone
CREATE TABLE `shipping_zone` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `countries` json NOT NULL,
  `states` json DEFAULT NULL,
  `postal_codes` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_shipping_zone_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: support_attachment
CREATE TABLE `support_attachment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ticket_id` int DEFAULT NULL,
  `message_id` int DEFAULT NULL,
  `filename` varchar(255) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `uploaded_by_type` varchar(20) NOT NULL,
  `uploaded_by_id` int DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ticket_id` (`ticket_id`),
  KEY `message_id` (`message_id`),
  KEY `idx_support_attachment_created_at` (`created_at`),
  CONSTRAINT `support_attachment_ibfk_1` FOREIGN KEY (`ticket_id`) REFERENCES `support_ticket` (`id`),
  CONSTRAINT `support_attachment_ibfk_2` FOREIGN KEY (`message_id`) REFERENCES `support_message` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: support_message
CREATE TABLE `support_message` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ticket_id` int NOT NULL,
  `sender_type` varchar(20) NOT NULL,
  `sender_id` int DEFAULT NULL,
  `sender_name` varchar(100) DEFAULT NULL,
  `message` text NOT NULL,
  `is_internal` tinyint(1) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ticket_id` (`ticket_id`),
  KEY `idx_support_message_created_at` (`created_at`),
  CONSTRAINT `support_message_ibfk_1` FOREIGN KEY (`ticket_id`) REFERENCES `support_ticket` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: support_ticket
CREATE TABLE `support_ticket` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ticket_id` varchar(20) NOT NULL,
  `user_id` int DEFAULT NULL,
  `contact_name` varchar(100) DEFAULT NULL,
  `contact_email` varchar(120) DEFAULT NULL,
  `contact_phone` varchar(20) DEFAULT NULL,
  `subject` varchar(200) NOT NULL,
  `category` varchar(50) NOT NULL,
  `priority` varchar(20) NOT NULL,
  `status` varchar(20) NOT NULL,
  `description` text NOT NULL,
  `order_number` varchar(50) DEFAULT NULL,
  `assigned_agent_id` int DEFAULT NULL,
  `assigned_agent_name` varchar(100) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `resolved_at` datetime DEFAULT NULL,
  `closed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ticket_id` (`ticket_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_support_ticket_status` (`status`),
  KEY `idx_support_ticket_created_at` (`created_at`),
  CONSTRAINT `support_ticket_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: sync_queue
CREATE TABLE `sync_queue` (
  `id` int NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `variant_id` int DEFAULT NULL,
  `channel_id` int DEFAULT NULL,
  `operation` varchar(20) NOT NULL,
  `priority` int NOT NULL,
  `data` json NOT NULL,
  `status` varchar(20) NOT NULL,
  `attempts` int NOT NULL,
  `max_attempts` int NOT NULL,
  `created_at` datetime NOT NULL,
  `scheduled_at` datetime NOT NULL,
  `started_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `last_error` text,
  `next_retry_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `variant_id` (`variant_id`),
  KEY `channel_id` (`channel_id`),
  KEY `idx_sync_queue_status` (`status`),
  KEY `idx_sync_queue_created_at` (`created_at`),
  CONSTRAINT `sync_queue_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  CONSTRAINT `sync_queue_ibfk_2` FOREIGN KEY (`variant_id`) REFERENCES `product_variant` (`id`),
  CONSTRAINT `sync_queue_ibfk_3` FOREIGN KEY (`channel_id`) REFERENCES `sales_channel` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: tax_rate
CREATE TABLE `tax_rate` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `rate` float NOT NULL,
  `country` varchar(100) NOT NULL,
  `state` varchar(100) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `product_categories` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_tax_rate_created_at` (`created_at`),
  KEY `idx_tax_rate_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: test_table
CREATE TABLE `test_table` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_test_table_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: tracking_events
CREATE TABLE `tracking_events` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_id` int NOT NULL,
  `shipment_id` int DEFAULT NULL,
  `tracking_number` varchar(100) NOT NULL,
  `status` varchar(50) NOT NULL,
  `event_type` varchar(50) DEFAULT NULL,
  `description` text,
  `location` varchar(255) DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  `carrier_code` varchar(50) NOT NULL,
  `carrier_status` varchar(100) DEFAULT NULL,
  `facility_name` varchar(255) DEFAULT NULL,
  `next_expected_event` varchar(100) DEFAULT NULL,
  `estimated_delivery` datetime DEFAULT NULL,
  `exception_code` varchar(50) DEFAULT NULL,
  `exception_description` text,
  `webhook_id` varchar(100) DEFAULT NULL,
  `raw_data` json DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `shipment_id` (`shipment_id`),
  KEY `ix_tracking_events_tracking_number` (`tracking_number`),
  KEY `ix_tracking_events_status` (`status`),
  KEY `ix_tracking_events_timestamp` (`timestamp`),
  KEY `ix_tracking_events_carrier_code` (`carrier_code`),
  KEY `idx_tracking_events_created_at` (`created_at`),
  CONSTRAINT `tracking_events_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `tracking_events_ibfk_2` FOREIGN KEY (`shipment_id`) REFERENCES `shipments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: user
CREATE TABLE `user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(120) NOT NULL,
  `password` varchar(128) NOT NULL,
  `address` varchar(200) DEFAULT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `bio` text,
  `newsletter_subscribed` tinyint(1) DEFAULT '1',
  `email_notifications` tinyint(1) DEFAULT '1',
  `sms_notifications` tinyint(1) DEFAULT '0',
  `preferred_language` varchar(10) DEFAULT 'en',
  `preferred_currency` varchar(3) DEFAULT 'INR',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_user_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: user_address
CREATE TABLE `user_address` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `address_type` varchar(20) NOT NULL,
  `is_default` tinyint(1) DEFAULT NULL,
  `full_name` varchar(100) NOT NULL,
  `address_line_1` varchar(255) NOT NULL,
  `address_line_2` varchar(255) DEFAULT NULL,
  `city` varchar(100) NOT NULL,
  `state` varchar(100) NOT NULL,
  `postal_code` varchar(20) NOT NULL,
  `country` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_user_address_created_at` (`created_at`),
  CONSTRAINT `user_address_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: user_behavior_profiles
CREATE TABLE `user_behavior_profiles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `category_preferences` json DEFAULT NULL,
  `brand_preferences` json DEFAULT NULL,
  `price_preferences` json DEFAULT NULL,
  `seasonal_preferences` json DEFAULT NULL,
  `total_interactions` int DEFAULT NULL,
  `avg_session_duration` float DEFAULT NULL,
  `engagement_score` float DEFAULT NULL,
  `purchase_frequency` float DEFAULT NULL,
  `avg_order_value` float DEFAULT NULL,
  `activity_patterns` json DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `last_interaction` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `idx_user_behavior_profiles_created_at` (`created_at`),
  CONSTRAINT `user_behavior_profiles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: user_interaction_logs
CREATE TABLE `user_interaction_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `product_id` int DEFAULT NULL,
  `session_id` varchar(100) NOT NULL,
  `interaction_type` varchar(50) NOT NULL,
  `interaction_value` float DEFAULT NULL,
  `context_data` json DEFAULT NULL,
  `interaction_metadata` json DEFAULT NULL,
  `timestamp` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `user_interaction_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `user_interaction_logs_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: user_o_auth
CREATE TABLE `user_o_auth` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `provider_id` int NOT NULL,
  `provider_user_id` varchar(255) NOT NULL,
  `provider_username` varchar(255) DEFAULT NULL,
  `provider_email` varchar(255) DEFAULT NULL,
  `provider_name` varchar(255) DEFAULT NULL,
  `provider_picture` varchar(500) DEFAULT NULL,
  `access_token` text,
  `refresh_token` text,
  `token_expires_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `last_login` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_provider_user` (`provider_id`,`provider_user_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_user_o_auth_created_at` (`created_at`),
  CONSTRAINT `user_o_auth_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `user_o_auth_ibfk_2` FOREIGN KEY (`provider_id`) REFERENCES `o_auth_provider` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: user_sessions
CREATE TABLE `user_sessions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) NOT NULL,
  `user_id` int DEFAULT NULL,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL,
  `duration_seconds` int DEFAULT NULL,
  `page_views` int DEFAULT NULL,
  `product_views` int DEFAULT NULL,
  `interactions_count` int DEFAULT NULL,
  `device_type` varchar(50) DEFAULT NULL,
  `browser` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `referrer` varchar(500) DEFAULT NULL,
  `session_data` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: users
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(120) DEFAULT NULL,
  `password` varchar(128) DEFAULT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `bio` text,
  `newsletter_subscribed` tinyint(1) DEFAULT NULL,
  `email_notifications` tinyint(1) DEFAULT NULL,
  `sms_notifications` tinyint(1) DEFAULT NULL,
  `preferred_language` varchar(10) DEFAULT NULL,
  `preferred_currency` varchar(3) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `address` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `phone` (`phone`),
  KEY `idx_users_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: visual_search_analytics
CREATE TABLE `visual_search_analytics` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `guest_session_id` varchar(100) DEFAULT NULL,
  `image_filename` varchar(255) DEFAULT NULL,
  `image_size` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `visual_search_analytics_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Table: wishlist
CREATE TABLE `wishlist` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `product_id` int NOT NULL,
  `added_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_product_wishlist` (`user_id`,`product_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `wishlist_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `wishlist_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
