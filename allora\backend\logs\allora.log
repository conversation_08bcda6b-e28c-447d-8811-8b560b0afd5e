2025-07-11 18:29:04 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 18:29:04 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 18:29:04 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 18:29:04 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 18:29:04 - startup - INFO - ============================================================
2025-07-11 18:29:04 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 18:29:04 - startup - INFO - ============================================================
2025-07-11 18:29:04 - startup - INFO - 📅 Startup Time: 2025-07-11T18:29:04.703348
2025-07-11 18:29:04 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 18:29:04 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 18:29:04 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 18:29:04 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 18:29:04 - startup - INFO - ============================================================
2025-07-11 18:29:04 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 18:29:05 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.105s]
2025-07-11 18:29:05 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 18:29:05 - allora - INFO - Search API blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 18:29:05 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 18:29:05 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 18:29:05 - tracking_system - INFO - Real-time tracking system started
2025-07-11 18:29:05 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 18:29:05 - notification_service - INFO - Notification delivery service started
2025-07-11 18:29:05 - allora - INFO - Notification service initialized successfully
2025-07-11 18:29:05 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 18:29:05 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 18:29:26 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 18:29:26 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 18:29:26 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 18:29:26 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 18:29:26 - startup - INFO - ============================================================
2025-07-11 18:29:26 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 18:29:26 - startup - INFO - ============================================================
2025-07-11 18:29:26 - startup - INFO - 📅 Startup Time: 2025-07-11T18:29:26.233458
2025-07-11 18:29:26 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 18:29:26 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 18:29:26 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 18:29:26 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 18:29:26 - startup - INFO - ============================================================
2025-07-11 18:29:26 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 18:29:26 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.005s]
2025-07-11 18:29:26 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 18:29:26 - allora - INFO - Search API blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 18:29:26 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 18:29:26 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 18:29:26 - tracking_system - INFO - Real-time tracking system started
2025-07-11 18:29:26 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 18:29:26 - notification_service - INFO - Notification delivery service started
2025-07-11 18:29:26 - allora - INFO - Notification service initialized successfully
2025-07-11 18:29:26 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 18:29:26 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 18:35:58 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 18:35:58 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 18:35:58 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 18:35:58 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 18:35:58 - startup - INFO - ============================================================
2025-07-11 18:35:58 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 18:35:58 - startup - INFO - ============================================================
2025-07-11 18:35:58 - startup - INFO - 📅 Startup Time: 2025-07-11T18:35:58.593784
2025-07-11 18:35:58 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 18:35:58 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 18:35:58 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 18:35:58 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 18:35:58 - startup - INFO - ============================================================
2025-07-11 18:35:58 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 18:35:59 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 18:35:59 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 18:35:59 - allora - INFO - Search API blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 18:35:59 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 18:35:59 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 18:35:59 - tracking_system - INFO - Real-time tracking system started
2025-07-11 18:35:59 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 18:35:59 - notification_service - INFO - Notification delivery service started
2025-07-11 18:35:59 - allora - INFO - Notification service initialized successfully
2025-07-11 18:35:59 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 18:35:59 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 18:35:59 - __main__ - INFO - Database connection established successfully
2025-07-11 18:36:00 - __main__ - ERROR - Error getting count for SearchAnalytics: (mysql.connector.errors.ProgrammingError) 1054 (42S22): Unknown column 'search_analytics.guest_session_id' in 'field list'
[SQL: SELECT count(*) AS count_1 
FROM (SELECT search_analytics.id AS search_analytics_id, search_analytics.user_id AS search_analytics_user_id, search_analytics.guest_session_id AS search_analytics_guest_session_id, search_analytics.search_query AS search_analytics_search_query, search_analytics.search_type AS search_analytics_search_type, search_analytics.results_count AS search_analytics_results_count, search_analytics.filters_applied AS search_analytics_filters_applied, search_analytics.clicked_results AS search_analytics_clicked_results, search_analytics.session_id AS search_analytics_session_id, search_analytics.ip_address AS search_analytics_ip_address, search_analytics.user_agent AS search_analytics_user_agent, search_analytics.created_at AS search_analytics_created_at 
FROM search_analytics) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-11 18:37:24 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 18:37:24 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 18:37:24 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 18:37:24 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 18:37:24 - startup - INFO - ============================================================
2025-07-11 18:37:24 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 18:37:24 - startup - INFO - ============================================================
2025-07-11 18:37:24 - startup - INFO - 📅 Startup Time: 2025-07-11T18:37:24.211090
2025-07-11 18:37:24 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 18:37:24 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 18:37:24 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 18:37:24 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 18:37:24 - startup - INFO - ============================================================
2025-07-11 18:37:24 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 18:37:24 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 18:37:24 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 18:37:24 - allora - INFO - Search API blueprint registered successfully
2025-07-11 18:37:24 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 18:37:24 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 18:37:24 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 18:37:24 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 18:37:24 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 18:37:24 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 18:37:24 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 18:37:25 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 18:37:25 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 18:37:25 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 18:37:25 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 18:37:25 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 18:37:25 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 18:37:25 - tracking_system - INFO - Real-time tracking system started
2025-07-11 18:37:25 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 18:37:25 - notification_service - INFO - Notification delivery service started
2025-07-11 18:37:25 - allora - INFO - Notification service initialized successfully
2025-07-11 18:37:25 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 18:37:25 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 18:37:25 - __main__ - INFO - Database connection established successfully
2025-07-11 18:37:25 - __main__ - INFO - Exporting specific tables: ['products', 'users']
2025-07-11 18:42:52 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 18:42:52 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 18:42:52 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 18:42:52 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 18:42:52 - startup - INFO - ============================================================
2025-07-11 18:42:52 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 18:42:52 - startup - INFO - ============================================================
2025-07-11 18:42:52 - startup - INFO - 📅 Startup Time: 2025-07-11T18:42:52.020356
2025-07-11 18:42:52 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 18:42:52 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 18:42:52 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 18:42:52 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 18:42:52 - startup - INFO - ============================================================
2025-07-11 18:42:52 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 18:42:52 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 18:42:52 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 18:42:52 - allora - INFO - Search API blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 18:42:52 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 18:42:52 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 18:42:52 - tracking_system - INFO - Real-time tracking system started
2025-07-11 18:42:52 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 18:42:52 - notification_service - INFO - Notification delivery service started
2025-07-11 18:42:52 - allora - INFO - Notification service initialized successfully
2025-07-11 18:42:52 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 18:42:52 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 18:42:53 - __main__ - INFO - Database connection established successfully
2025-07-11 18:42:53 - __main__ - INFO - Exporting specific tables: ['products']
2025-07-11 18:50:54 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 18:50:54 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 18:50:54 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 18:50:54 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 18:50:54 - startup - INFO - ============================================================
2025-07-11 18:50:54 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 18:50:54 - startup - INFO - ============================================================
2025-07-11 18:50:54 - startup - INFO - 📅 Startup Time: 2025-07-11T18:50:54.174579
2025-07-11 18:50:54 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 18:50:54 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 18:50:54 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 18:50:54 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 18:50:54 - startup - INFO - ============================================================
2025-07-11 18:50:54 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 18:50:54 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-11 18:50:54 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 18:50:54 - allora - INFO - Search API blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 18:50:54 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 18:50:54 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 18:50:54 - tracking_system - INFO - Real-time tracking system started
2025-07-11 18:50:54 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 18:50:54 - notification_service - INFO - Notification delivery service started
2025-07-11 18:50:54 - allora - INFO - Notification service initialized successfully
2025-07-11 18:50:54 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 18:50:54 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 18:50:55 - __main__ - INFO - Database connection established successfully
2025-07-11 18:50:55 - __main__ - INFO - Exporting specific tables: ['products', 'users']
2025-07-11 18:51:21 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 18:51:21 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 18:51:21 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 18:51:21 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 18:51:21 - startup - INFO - ============================================================
2025-07-11 18:51:21 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 18:51:21 - startup - INFO - ============================================================
2025-07-11 18:51:21 - startup - INFO - 📅 Startup Time: 2025-07-11T18:51:21.468525
2025-07-11 18:51:21 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 18:51:21 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 18:51:21 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 18:51:21 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 18:51:21 - startup - INFO - ============================================================
2025-07-11 18:51:21 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 18:51:21 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 18:51:21 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 18:51:22 - allora - INFO - Search API blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 18:51:22 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 18:51:22 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 18:51:22 - tracking_system - INFO - Real-time tracking system started
2025-07-11 18:51:22 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 18:51:22 - notification_service - INFO - Notification delivery service started
2025-07-11 18:51:22 - allora - INFO - Notification service initialized successfully
2025-07-11 18:51:22 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 18:51:22 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 18:51:22 - __main__ - INFO - Database connection established successfully
2025-07-11 18:51:23 - __main__ - ERROR - Error getting table info for SearchAnalytics: (mysql.connector.errors.ProgrammingError) 1054 (42S22): Unknown column 'search_analytics.guest_session_id' in 'field list'
[SQL: SELECT count(*) AS count_1 
FROM (SELECT search_analytics.id AS search_analytics_id, search_analytics.user_id AS search_analytics_user_id, search_analytics.guest_session_id AS search_analytics_guest_session_id, search_analytics.search_query AS search_analytics_search_query, search_analytics.search_type AS search_analytics_search_type, search_analytics.results_count AS search_analytics_results_count, search_analytics.filters_applied AS search_analytics_filters_applied, search_analytics.clicked_results AS search_analytics_clicked_results, search_analytics.session_id AS search_analytics_session_id, search_analytics.ip_address AS search_analytics_ip_address, search_analytics.user_agent AS search_analytics_user_agent, search_analytics.created_at AS search_analytics_created_at 
FROM search_analytics) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-11 18:56:34 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 18:56:34 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 18:56:34 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 18:56:34 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 18:56:34 - startup - INFO - ============================================================
2025-07-11 18:56:34 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 18:56:34 - startup - INFO - ============================================================
2025-07-11 18:56:34 - startup - INFO - 📅 Startup Time: 2025-07-11T18:56:34.156907
2025-07-11 18:56:34 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 18:56:34 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 18:56:34 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 18:56:34 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 18:56:34 - startup - INFO - ============================================================
2025-07-11 18:56:34 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 18:56:34 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 18:56:34 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 18:56:34 - allora - INFO - Search API blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 18:56:34 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 18:56:34 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 18:56:34 - tracking_system - INFO - Real-time tracking system started
2025-07-11 18:56:34 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 18:56:34 - notification_service - INFO - Notification delivery service started
2025-07-11 18:56:34 - allora - INFO - Notification service initialized successfully
2025-07-11 18:56:34 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 18:56:34 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 18:56:35 - __main__ - INFO - Database connection established successfully
2025-07-11 18:56:35 - __main__ - ERROR - Error getting count for SearchAnalytics: (mysql.connector.errors.ProgrammingError) 1054 (42S22): Unknown column 'search_analytics.guest_session_id' in 'field list'
[SQL: SELECT count(*) AS count_1 
FROM (SELECT search_analytics.id AS search_analytics_id, search_analytics.user_id AS search_analytics_user_id, search_analytics.guest_session_id AS search_analytics_guest_session_id, search_analytics.search_query AS search_analytics_search_query, search_analytics.search_type AS search_analytics_search_type, search_analytics.results_count AS search_analytics_results_count, search_analytics.filters_applied AS search_analytics_filters_applied, search_analytics.clicked_results AS search_analytics_clicked_results, search_analytics.session_id AS search_analytics_session_id, search_analytics.ip_address AS search_analytics_ip_address, search_analytics.user_agent AS search_analytics_user_agent, search_analytics.created_at AS search_analytics_created_at 
FROM search_analytics) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-11 18:57:37 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 18:57:37 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 18:57:37 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 18:57:37 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 18:57:37 - startup - INFO - ============================================================
2025-07-11 18:57:37 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 18:57:37 - startup - INFO - ============================================================
2025-07-11 18:57:37 - startup - INFO - 📅 Startup Time: 2025-07-11T18:57:37.233205
2025-07-11 18:57:37 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 18:57:37 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 18:57:37 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 18:57:37 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 18:57:37 - startup - INFO - ============================================================
2025-07-11 18:57:37 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 18:57:37 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 18:57:37 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 18:57:37 - allora - INFO - Search API blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 18:57:37 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 18:57:37 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 18:57:37 - tracking_system - INFO - Real-time tracking system started
2025-07-11 18:57:37 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 18:57:37 - notification_service - INFO - Notification delivery service started
2025-07-11 18:57:37 - allora - INFO - Notification service initialized successfully
2025-07-11 18:57:37 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 18:57:37 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 18:58:02 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 18:58:02 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 18:58:02 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 18:58:02 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 18:58:02 - startup - INFO - ============================================================
2025-07-11 18:58:02 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 18:58:02 - startup - INFO - ============================================================
2025-07-11 18:58:02 - startup - INFO - 📅 Startup Time: 2025-07-11T18:58:02.701549
2025-07-11 18:58:02 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 18:58:02 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 18:58:02 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 18:58:02 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 18:58:02 - startup - INFO - ============================================================
2025-07-11 18:58:02 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 18:58:03 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 18:58:03 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 18:58:03 - allora - INFO - Search API blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 18:58:03 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 18:58:03 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 18:58:03 - tracking_system - INFO - Real-time tracking system started
2025-07-11 18:58:03 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 18:58:03 - notification_service - INFO - Notification delivery service started
2025-07-11 18:58:03 - allora - INFO - Notification service initialized successfully
2025-07-11 18:58:03 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 18:58:03 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 18:58:03 - __main__ - INFO - Database connection established successfully
2025-07-11 18:58:03 - __main__ - INFO - Exporting specific tables: ['products', 'users', 'orders']
2025-07-11 20:37:42 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 20:37:42 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 20:37:42 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 20:37:42 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 20:37:42 - startup - INFO - ============================================================
2025-07-11 20:37:42 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 20:37:42 - startup - INFO - ============================================================
2025-07-11 20:37:42 - startup - INFO - 📅 Startup Time: 2025-07-11T20:37:42.579095
2025-07-11 20:37:42 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 20:37:42 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 20:37:42 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 20:37:42 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 20:37:42 - startup - INFO - ============================================================
2025-07-11 20:37:42 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 20:37:43 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 20:37:43 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 20:37:43 - allora - INFO - Search API blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 20:37:43 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 20:37:43 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 20:37:43 - tracking_system - INFO - Real-time tracking system started
2025-07-11 20:37:43 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 20:37:43 - notification_service - INFO - Notification delivery service started
2025-07-11 20:37:43 - allora - INFO - Notification service initialized successfully
2025-07-11 20:37:43 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 20:37:43 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 20:37:44 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-11 20:37:44 - allora - INFO - Recommendation system initialized successfully
2025-07-11 20:37:44 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-11 20:47:46 - allora - WARNING - 404 error for path: /favicon.ico
2025-07-11 22:19:49 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 22:19:49 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 22:19:49 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 22:19:49 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 22:19:49 - startup - INFO - ============================================================
2025-07-11 22:19:49 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 22:19:49 - startup - INFO - ============================================================
2025-07-11 22:19:49 - startup - INFO - 📅 Startup Time: 2025-07-11T22:19:49.656045
2025-07-11 22:19:49 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 22:19:49 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 22:19:49 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 22:19:49 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 22:19:49 - startup - INFO - ============================================================
2025-07-11 22:19:49 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 22:19:55 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.155s]
2025-07-11 22:19:55 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-07-11 22:19:55 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 0 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001D7CD289A50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001D7CD289A50>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-11 22:19:59 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.080s]
2025-07-11 22:19:59 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 2 times in a row, putting on 2 second timeout
2025-07-11 22:19:59 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 1 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001D7CD289D80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001D7CD289D80>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-11 22:19:59 - elastic_transport.node_pool - INFO - Resurrected node <Urllib3HttpNode(http://localhost:9200)> (force=False)
2025-07-11 22:21:14 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 22:21:14 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 22:21:14 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 22:21:14 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 22:21:14 - startup - INFO - ============================================================
2025-07-11 22:21:14 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 22:21:14 - startup - INFO - ============================================================
2025-07-11 22:21:14 - startup - INFO - 📅 Startup Time: 2025-07-11T22:21:14.453991
2025-07-11 22:21:14 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 22:21:14 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 22:21:14 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 22:21:14 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 22:21:14 - startup - INFO - ============================================================
2025-07-11 22:21:14 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 22:21:19 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.047s]
2025-07-11 22:21:19 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-07-11 22:21:19 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 0 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001C9B6B08F40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001C9B6B08F40>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-11 22:21:23 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.043s]
2025-07-11 22:21:23 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 2 times in a row, putting on 2 second timeout
2025-07-11 22:21:23 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 1 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001C9B6B09270>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001C9B6B09270>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-11 22:21:23 - elastic_transport.node_pool - INFO - Resurrected node <Urllib3HttpNode(http://localhost:9200)> (force=False)
2025-07-11 22:21:27 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.023s]
2025-07-11 22:21:27 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 3 times in a row, putting on 4 second timeout
2025-07-11 22:21:27 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 2 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001C9B6B094B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001C9B6B094B0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-11 22:21:27 - elastic_transport.node_pool - INFO - Resurrected node <Urllib3HttpNode(http://localhost:9200)> (force=False)
2025-07-11 22:21:31 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.063s]
2025-07-11 22:21:31 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 4 times in a row, putting on 8 second timeout
2025-07-11 22:21:31 - elasticsearch_config - WARNING - Failed to connect to Elasticsearch - running without search functionality
2025-07-11 22:21:31 - allora - INFO - Search API blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 22:21:31 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 22:21:31 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 22:21:31 - tracking_system - INFO - Real-time tracking system started
2025-07-11 22:21:31 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 22:21:31 - notification_service - INFO - Notification delivery service started
2025-07-11 22:21:31 - allora - INFO - Notification service initialized successfully
2025-07-11 22:21:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:29:01 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 22:29:01 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 22:29:01 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 22:29:01 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 22:29:01 - startup - INFO - ============================================================
2025-07-11 22:29:01 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 22:29:01 - startup - INFO - ============================================================
2025-07-11 22:29:01 - startup - INFO - 📅 Startup Time: 2025-07-11T22:29:01.899082
2025-07-11 22:29:01 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 22:29:01 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 22:29:01 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 22:29:01 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 22:29:01 - startup - INFO - ============================================================
2025-07-11 22:29:01 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 22:29:06 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.051s]
2025-07-11 22:29:06 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-07-11 22:29:06 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 0 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001FB6FEE6C20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001FB6FEE6C20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-11 22:29:10 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.042s]
2025-07-11 22:29:10 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 2 times in a row, putting on 2 second timeout
2025-07-11 22:29:10 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 1 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001FB6FEE6EF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001FB6FEE6EF0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-11 22:29:10 - elastic_transport.node_pool - INFO - Resurrected node <Urllib3HttpNode(http://localhost:9200)> (force=False)
2025-07-11 22:29:14 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.061s]
2025-07-11 22:29:14 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 3 times in a row, putting on 4 second timeout
2025-07-11 22:29:14 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 2 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001FB6FEE7130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001FB6FEE7130>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-11 22:29:14 - elastic_transport.node_pool - INFO - Resurrected node <Urllib3HttpNode(http://localhost:9200)> (force=False)
2025-07-11 22:29:18 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.077s]
2025-07-11 22:29:18 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 4 times in a row, putting on 8 second timeout
2025-07-11 22:29:18 - elasticsearch_config - WARNING - Failed to connect to Elasticsearch - running without search functionality
2025-07-11 22:29:18 - allora - INFO - Search API blueprint registered successfully
2025-07-11 22:29:18 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 22:29:18 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 22:29:18 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 22:29:18 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 22:29:18 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 22:29:18 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 22:29:19 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 22:29:19 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 22:29:19 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 22:29:19 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 22:29:19 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 22:29:19 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 22:29:19 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 22:29:19 - tracking_system - INFO - Real-time tracking system started
2025-07-11 22:29:19 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 22:29:19 - notification_service - INFO - Notification delivery service started
2025-07-11 22:29:19 - allora - INFO - Notification service initialized successfully
2025-07-11 22:29:19 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:31:57 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 22:31:57 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 22:31:57 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 22:31:57 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 22:31:57 - startup - INFO - ============================================================
2025-07-11 22:31:57 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 22:31:57 - startup - INFO - ============================================================
2025-07-11 22:31:57 - startup - INFO - 📅 Startup Time: 2025-07-11T22:31:57.980253
2025-07-11 22:31:57 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 22:31:57 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 22:31:57 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 22:31:57 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 22:31:57 - startup - INFO - ============================================================
2025-07-11 22:31:57 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 22:31:58 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.115s]
2025-07-11 22:31:58 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 22:31:58 - allora - INFO - Search API blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 22:31:58 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 22:31:58 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 22:31:58 - tracking_system - INFO - Real-time tracking system started
2025-07-11 22:31:58 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 22:31:58 - notification_service - INFO - Notification delivery service started
2025-07-11 22:31:58 - allora - INFO - Notification service initialized successfully
2025-07-11 22:31:58 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:32:43 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 22:32:43 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 22:32:43 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 22:32:43 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 22:32:43 - startup - INFO - ============================================================
2025-07-11 22:32:43 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 22:32:43 - startup - INFO - ============================================================
2025-07-11 22:32:43 - startup - INFO - 📅 Startup Time: 2025-07-11T22:32:43.536807
2025-07-11 22:32:43 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 22:32:43 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 22:32:43 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 22:32:43 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 22:32:43 - startup - INFO - ============================================================
2025-07-11 22:32:43 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 22:32:44 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.148s]
2025-07-11 22:32:44 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 22:32:44 - allora - INFO - Search API blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 22:32:44 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 22:32:44 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 22:32:44 - tracking_system - INFO - Real-time tracking system started
2025-07-11 22:32:44 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 22:32:44 - notification_service - INFO - Notification delivery service started
2025-07-11 22:32:44 - allora - INFO - Notification service initialized successfully
2025-07-11 22:32:44 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:33:57 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 22:33:57 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 22:33:57 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 22:33:57 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 22:33:57 - startup - INFO - ============================================================
2025-07-11 22:33:57 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 22:33:57 - startup - INFO - ============================================================
2025-07-11 22:33:57 - startup - INFO - 📅 Startup Time: 2025-07-11T22:33:57.043066
2025-07-11 22:33:57 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 22:33:57 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 22:33:57 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 22:33:57 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 22:33:57 - startup - INFO - ============================================================
2025-07-11 22:33:57 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 22:33:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.009s]
2025-07-11 22:33:57 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 22:33:57 - allora - INFO - Search API blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 22:33:57 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 22:33:57 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 22:33:57 - tracking_system - INFO - Real-time tracking system started
2025-07-11 22:33:57 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 22:33:57 - notification_service - INFO - Notification delivery service started
2025-07-11 22:33:57 - allora - INFO - Notification service initialized successfully
2025-07-11 22:33:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:34:44 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 22:34:44 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 22:34:44 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 22:34:44 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 22:34:44 - startup - INFO - ============================================================
2025-07-11 22:34:44 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 22:34:44 - startup - INFO - ============================================================
2025-07-11 22:34:44 - startup - INFO - 📅 Startup Time: 2025-07-11T22:34:44.369946
2025-07-11 22:34:44 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 22:34:44 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 22:34:44 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 22:34:44 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 22:34:44 - startup - INFO - ============================================================
2025-07-11 22:34:44 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 22:34:44 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 22:34:44 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 22:34:45 - allora - INFO - Search API blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 22:34:45 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 22:34:45 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 22:34:45 - tracking_system - INFO - Real-time tracking system started
2025-07-11 22:34:45 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 22:34:45 - notification_service - INFO - Notification delivery service started
2025-07-11 22:34:45 - allora - INFO - Notification service initialized successfully
2025-07-11 22:34:45 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:34:46 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-11 22:34:46 - allora - INFO - Recommendation system initialized successfully
2025-07-11 22:34:46 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-11 22:36:17 - waitress.queue - WARNING - Task queue depth is 1
2025-07-11 22:55:58 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 22:55:58 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 22:55:58 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 22:55:58 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 22:55:58 - startup - INFO - ============================================================
2025-07-11 22:55:58 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 22:55:58 - startup - INFO - ============================================================
2025-07-11 22:55:58 - startup - INFO - 📅 Startup Time: 2025-07-11T22:55:58.437217
2025-07-11 22:55:58 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 22:55:58 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 22:55:58 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 22:55:58 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 22:55:58 - startup - INFO - ============================================================
2025-07-11 22:55:58 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 22:55:59 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 22:55:59 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 22:55:59 - allora - INFO - Search API blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 22:55:59 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 22:55:59 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 22:55:59 - tracking_system - INFO - Real-time tracking system started
2025-07-11 22:55:59 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 22:55:59 - notification_service - INFO - Notification delivery service started
2025-07-11 22:55:59 - allora - INFO - Notification service initialized successfully
2025-07-11 22:55:59 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:57:22 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 22:57:22 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 22:57:22 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 22:57:22 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 22:57:22 - startup - INFO - ============================================================
2025-07-11 22:57:22 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 22:57:22 - startup - INFO - ============================================================
2025-07-11 22:57:22 - startup - INFO - 📅 Startup Time: 2025-07-11T22:57:22.232445
2025-07-11 22:57:22 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 22:57:22 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 22:57:22 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 22:57:22 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 22:57:22 - startup - INFO - ============================================================
2025-07-11 22:57:22 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 22:57:22 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-11 22:57:22 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 22:57:22 - allora - INFO - Search API blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 22:57:22 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 22:57:22 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 22:57:22 - tracking_system - INFO - Real-time tracking system started
2025-07-11 22:57:22 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 22:57:22 - notification_service - INFO - Notification delivery service started
2025-07-11 22:57:22 - allora - INFO - Notification service initialized successfully
2025-07-11 22:57:22 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 22:58:12 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 22:58:12 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 22:58:12 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 22:58:12 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 22:58:12 - startup - INFO - ============================================================
2025-07-11 22:58:12 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 22:58:12 - startup - INFO - ============================================================
2025-07-11 22:58:12 - startup - INFO - 📅 Startup Time: 2025-07-11T22:58:12.861434
2025-07-11 22:58:12 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 22:58:12 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 22:58:12 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 22:58:12 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 22:58:12 - startup - INFO - ============================================================
2025-07-11 22:58:12 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 22:58:13 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 22:58:13 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 22:58:13 - allora - INFO - Search API blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 22:58:13 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 22:58:13 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 22:58:13 - tracking_system - INFO - Real-time tracking system started
2025-07-11 22:58:13 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 22:58:13 - notification_service - INFO - Notification delivery service started
2025-07-11 22:58:13 - allora - INFO - Notification service initialized successfully
2025-07-11 22:58:13 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:00:19 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:00:19 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:00:19 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:00:19 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:00:19 - startup - INFO - ============================================================
2025-07-11 23:00:19 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:00:19 - startup - INFO - ============================================================
2025-07-11 23:00:19 - startup - INFO - 📅 Startup Time: 2025-07-11T23:00:19.665773
2025-07-11 23:00:19 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:00:19 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:00:19 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:00:19 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:00:19 - startup - INFO - ============================================================
2025-07-11 23:00:19 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:00:20 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.128s]
2025-07-11 23:00:20 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:00:20 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:00:20 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:00:20 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:00:20 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:00:20 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:00:20 - notification_service - INFO - Notification delivery service started
2025-07-11 23:00:20 - allora - INFO - Notification service initialized successfully
2025-07-11 23:00:20 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:00:58 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:00:58 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:00:58 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:00:58 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:00:58 - startup - INFO - ============================================================
2025-07-11 23:00:58 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:00:58 - startup - INFO - ============================================================
2025-07-11 23:00:58 - startup - INFO - 📅 Startup Time: 2025-07-11T23:00:58.169792
2025-07-11 23:00:58 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:00:58 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:00:58 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:00:58 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:00:58 - startup - INFO - ============================================================
2025-07-11 23:00:58 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:00:58 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.215s]
2025-07-11 23:00:58 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:00:59 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:00:59 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:00:59 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:00:59 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:00:59 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:00:59 - notification_service - INFO - Notification delivery service started
2025-07-11 23:00:59 - allora - INFO - Notification service initialized successfully
2025-07-11 23:00:59 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:01:39 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:01:39 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:01:39 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:01:39 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:01:39 - startup - INFO - ============================================================
2025-07-11 23:01:39 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:01:39 - startup - INFO - ============================================================
2025-07-11 23:01:39 - startup - INFO - 📅 Startup Time: 2025-07-11T23:01:39.231213
2025-07-11 23:01:39 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:01:39 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:01:39 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:01:39 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:01:39 - startup - INFO - ============================================================
2025-07-11 23:01:39 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:01:39 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:01:39 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:01:39 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:01:39 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:01:39 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:01:39 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:01:40 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:01:40 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:01:40 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:01:40 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:01:40 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:01:40 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:01:40 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:01:40 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:01:40 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:01:40 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:01:40 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:01:40 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:01:40 - notification_service - INFO - Notification delivery service started
2025-07-11 23:01:40 - allora - INFO - Notification service initialized successfully
2025-07-11 23:01:40 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:02:20 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:02:20 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:02:20 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:02:20 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:02:20 - startup - INFO - ============================================================
2025-07-11 23:02:20 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:02:20 - startup - INFO - ============================================================
2025-07-11 23:02:20 - startup - INFO - 📅 Startup Time: 2025-07-11T23:02:20.115937
2025-07-11 23:02:20 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:02:20 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:02:20 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:02:20 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:02:20 - startup - INFO - ============================================================
2025-07-11 23:02:20 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:02:20 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-11 23:02:20 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:02:20 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:02:20 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:02:20 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:02:20 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:02:20 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:02:20 - notification_service - INFO - Notification delivery service started
2025-07-11 23:02:20 - allora - INFO - Notification service initialized successfully
2025-07-11 23:02:20 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:28:10 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:28:10 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:28:10 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:28:10 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:28:10 - startup - INFO - ============================================================
2025-07-11 23:28:10 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:28:10 - startup - INFO - ============================================================
2025-07-11 23:28:10 - startup - INFO - 📅 Startup Time: 2025-07-11T23:28:10.970203
2025-07-11 23:28:10 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:28:10 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:28:10 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:28:10 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:28:10 - startup - INFO - ============================================================
2025-07-11 23:28:10 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:28:11 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-11 23:28:11 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:28:11 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:28:11 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:28:11 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:28:11 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:28:11 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:28:11 - notification_service - INFO - Notification delivery service started
2025-07-11 23:28:11 - allora - INFO - Notification service initialized successfully
2025-07-11 23:28:11 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:28:47 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:28:47 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:28:47 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:28:47 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:28:47 - startup - INFO - ============================================================
2025-07-11 23:28:47 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:28:47 - startup - INFO - ============================================================
2025-07-11 23:28:47 - startup - INFO - 📅 Startup Time: 2025-07-11T23:28:47.152554
2025-07-11 23:28:47 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:28:47 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:28:47 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:28:47 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:28:47 - startup - INFO - ============================================================
2025-07-11 23:28:47 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:28:47 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:28:47 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:28:47 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:28:47 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:28:47 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:28:47 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:28:47 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:28:47 - notification_service - INFO - Notification delivery service started
2025-07-11 23:28:47 - allora - INFO - Notification service initialized successfully
2025-07-11 23:28:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:29:53 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:29:53 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:29:53 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:29:53 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:29:53 - startup - INFO - ============================================================
2025-07-11 23:29:53 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:29:53 - startup - INFO - ============================================================
2025-07-11 23:29:53 - startup - INFO - 📅 Startup Time: 2025-07-11T23:29:53.378483
2025-07-11 23:29:53 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:29:53 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:29:53 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:29:53 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:29:53 - startup - INFO - ============================================================
2025-07-11 23:29:53 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:29:53 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 23:29:53 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:29:54 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:29:54 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:29:54 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:29:54 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:29:54 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:29:54 - notification_service - INFO - Notification delivery service started
2025-07-11 23:29:54 - allora - INFO - Notification service initialized successfully
2025-07-11 23:29:54 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:31:50 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:31:50 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:31:50 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:31:50 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:31:50 - startup - INFO - ============================================================
2025-07-11 23:31:50 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:31:50 - startup - INFO - ============================================================
2025-07-11 23:31:50 - startup - INFO - 📅 Startup Time: 2025-07-11T23:31:50.834991
2025-07-11 23:31:50 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:31:50 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:31:50 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:31:50 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:31:50 - startup - INFO - ============================================================
2025-07-11 23:31:50 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:31:51 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.016s]
2025-07-11 23:31:51 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:31:51 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:31:51 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:31:51 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:31:51 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:31:51 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:31:51 - notification_service - INFO - Notification delivery service started
2025-07-11 23:31:51 - allora - INFO - Notification service initialized successfully
2025-07-11 23:31:51 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:33:00 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:33:00 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:33:00 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:33:00 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:33:00 - startup - INFO - ============================================================
2025-07-11 23:33:00 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:33:00 - startup - INFO - ============================================================
2025-07-11 23:33:00 - startup - INFO - 📅 Startup Time: 2025-07-11T23:33:00.289751
2025-07-11 23:33:00 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:33:00 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:33:00 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:33:00 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:33:00 - startup - INFO - ============================================================
2025-07-11 23:33:00 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:33:00 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.010s]
2025-07-11 23:33:00 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:33:00 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:33:00 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:33:01 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:33:01 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:33:01 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:33:01 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:33:01 - notification_service - INFO - Notification delivery service started
2025-07-11 23:33:01 - allora - INFO - Notification service initialized successfully
2025-07-11 23:33:01 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:34:01 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:34:01 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:34:01 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:34:01 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:34:01 - startup - INFO - ============================================================
2025-07-11 23:34:01 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:34:01 - startup - INFO - ============================================================
2025-07-11 23:34:01 - startup - INFO - 📅 Startup Time: 2025-07-11T23:34:01.197459
2025-07-11 23:34:01 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:34:01 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:34:01 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:34:01 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:34:01 - startup - INFO - ============================================================
2025-07-11 23:34:01 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:34:01 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:34:01 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:34:01 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:34:01 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:34:01 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:34:01 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:34:01 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:34:01 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:34:01 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:34:02 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:34:02 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:34:02 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:34:02 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:34:02 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:34:02 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:34:02 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:34:02 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:34:02 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:34:02 - notification_service - INFO - Notification delivery service started
2025-07-11 23:34:02 - allora - INFO - Notification service initialized successfully
2025-07-11 23:34:02 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:35:30 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:35:30 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:35:30 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:35:30 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:35:30 - startup - INFO - ============================================================
2025-07-11 23:35:30 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:35:30 - startup - INFO - ============================================================
2025-07-11 23:35:30 - startup - INFO - 📅 Startup Time: 2025-07-11T23:35:30.697305
2025-07-11 23:35:30 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:35:30 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:35:30 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:35:30 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:35:30 - startup - INFO - ============================================================
2025-07-11 23:35:30 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:35:31 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:35:31 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:35:31 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:35:31 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:35:31 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:35:31 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:35:31 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:35:31 - notification_service - INFO - Notification delivery service started
2025-07-11 23:35:31 - allora - INFO - Notification service initialized successfully
2025-07-11 23:35:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:36:49 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:36:49 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:36:49 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:36:49 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:36:49 - startup - INFO - ============================================================
2025-07-11 23:36:49 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:36:49 - startup - INFO - ============================================================
2025-07-11 23:36:49 - startup - INFO - 📅 Startup Time: 2025-07-11T23:36:49.146576
2025-07-11 23:36:49 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:36:49 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:36:49 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:36:49 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:36:49 - startup - INFO - ============================================================
2025-07-11 23:36:49 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:36:49 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:36:49 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:36:49 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:36:49 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:36:49 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:36:49 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:36:49 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:36:49 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:36:49 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:36:50 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:36:50 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:36:50 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:36:50 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:36:50 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:36:50 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:36:50 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:36:50 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:36:50 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:36:50 - notification_service - INFO - Notification delivery service started
2025-07-11 23:36:50 - allora - INFO - Notification service initialized successfully
2025-07-11 23:36:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:38:28 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:38:28 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:38:28 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:38:28 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:38:28 - startup - INFO - ============================================================
2025-07-11 23:38:28 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:38:28 - startup - INFO - ============================================================
2025-07-11 23:38:28 - startup - INFO - 📅 Startup Time: 2025-07-11T23:38:28.116205
2025-07-11 23:38:28 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:38:28 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:38:28 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:38:28 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:38:28 - startup - INFO - ============================================================
2025-07-11 23:38:28 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:38:28 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 23:38:28 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:38:28 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:38:28 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:38:28 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:38:28 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:38:28 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:38:28 - notification_service - INFO - Notification delivery service started
2025-07-11 23:38:28 - allora - INFO - Notification service initialized successfully
2025-07-11 23:38:28 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:39:46 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:39:46 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:39:46 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:39:46 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:39:46 - startup - INFO - ============================================================
2025-07-11 23:39:46 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:39:46 - startup - INFO - ============================================================
2025-07-11 23:39:46 - startup - INFO - 📅 Startup Time: 2025-07-11T23:39:46.729480
2025-07-11 23:39:46 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:39:46 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:39:46 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:39:46 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:39:46 - startup - INFO - ============================================================
2025-07-11 23:39:46 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:39:47 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:39:47 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:39:47 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:39:47 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:39:47 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:39:47 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:39:47 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:39:47 - notification_service - INFO - Notification delivery service started
2025-07-11 23:39:47 - allora - INFO - Notification service initialized successfully
2025-07-11 23:39:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:40:47 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:40:47 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:40:47 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:40:47 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:40:47 - startup - INFO - ============================================================
2025-07-11 23:40:47 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:40:47 - startup - INFO - ============================================================
2025-07-11 23:40:47 - startup - INFO - 📅 Startup Time: 2025-07-11T23:40:47.074728
2025-07-11 23:40:47 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:40:47 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:40:47 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:40:47 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:40:47 - startup - INFO - ============================================================
2025-07-11 23:40:47 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:40:47 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:40:47 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:40:47 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:40:47 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:40:47 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:40:47 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:40:47 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:40:47 - notification_service - INFO - Notification delivery service started
2025-07-11 23:40:47 - allora - INFO - Notification service initialized successfully
2025-07-11 23:40:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:42:01 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:42:01 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:42:01 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:42:01 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:42:01 - startup - INFO - ============================================================
2025-07-11 23:42:01 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:42:01 - startup - INFO - ============================================================
2025-07-11 23:42:01 - startup - INFO - 📅 Startup Time: 2025-07-11T23:42:01.515057
2025-07-11 23:42:01 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:42:01 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:42:01 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:42:01 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:42:01 - startup - INFO - ============================================================
2025-07-11 23:42:01 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:42:02 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 23:42:02 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:42:02 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:42:02 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:42:02 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:42:02 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:42:02 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:42:02 - notification_service - INFO - Notification delivery service started
2025-07-11 23:42:02 - allora - INFO - Notification service initialized successfully
2025-07-11 23:42:02 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:43:42 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:43:42 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:43:42 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:43:42 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:43:42 - startup - INFO - ============================================================
2025-07-11 23:43:42 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:43:42 - startup - INFO - ============================================================
2025-07-11 23:43:42 - startup - INFO - 📅 Startup Time: 2025-07-11T23:43:42.703230
2025-07-11 23:43:42 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:43:42 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:43:42 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:43:42 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:43:42 - startup - INFO - ============================================================
2025-07-11 23:43:42 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:43:43 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:43:43 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:43:43 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:43:43 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:43:43 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:43:43 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:43:43 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:43:43 - notification_service - INFO - Notification delivery service started
2025-07-11 23:43:43 - allora - INFO - Notification service initialized successfully
2025-07-11 23:43:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:45:21 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:45:21 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:45:21 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:45:21 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:45:21 - startup - INFO - ============================================================
2025-07-11 23:45:21 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:45:21 - startup - INFO - ============================================================
2025-07-11 23:45:21 - startup - INFO - 📅 Startup Time: 2025-07-11T23:45:21.037137
2025-07-11 23:45:21 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:45:21 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:45:21 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:45:21 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:45:21 - startup - INFO - ============================================================
2025-07-11 23:45:21 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:45:21 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:45:21 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:45:21 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:45:21 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:45:21 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:45:21 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:45:21 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:45:21 - notification_service - INFO - Notification delivery service started
2025-07-11 23:45:21 - allora - INFO - Notification service initialized successfully
2025-07-11 23:45:21 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:49:49 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:49:49 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:49:49 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:49:49 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:49:49 - startup - INFO - ============================================================
2025-07-11 23:49:49 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:49:49 - startup - INFO - ============================================================
2025-07-11 23:49:49 - startup - INFO - 📅 Startup Time: 2025-07-11T23:49:49.075986
2025-07-11 23:49:49 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:49:49 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:49:49 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:49:49 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:49:49 - startup - INFO - ============================================================
2025-07-11 23:49:49 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:49:49 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 23:49:49 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:49:49 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:49:49 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:49:49 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:49:49 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:49:49 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:49:49 - notification_service - INFO - Notification delivery service started
2025-07-11 23:49:49 - allora - INFO - Notification service initialized successfully
2025-07-11 23:49:49 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:51:06 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:51:06 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:51:06 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:51:06 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:51:06 - startup - INFO - ============================================================
2025-07-11 23:51:06 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:51:06 - startup - INFO - ============================================================
2025-07-11 23:51:06 - startup - INFO - 📅 Startup Time: 2025-07-11T23:51:06.089596
2025-07-11 23:51:06 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:51:06 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:51:06 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:51:06 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:51:06 - startup - INFO - ============================================================
2025-07-11 23:51:06 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:51:06 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.009s]
2025-07-11 23:51:06 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:51:06 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:51:06 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:51:06 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:51:06 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:51:06 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:51:06 - notification_service - INFO - Notification delivery service started
2025-07-11 23:51:06 - allora - INFO - Notification service initialized successfully
2025-07-11 23:51:06 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:52:32 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:52:32 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:52:32 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:52:32 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:52:32 - startup - INFO - ============================================================
2025-07-11 23:52:32 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:52:32 - startup - INFO - ============================================================
2025-07-11 23:52:32 - startup - INFO - 📅 Startup Time: 2025-07-11T23:52:32.085638
2025-07-11 23:52:32 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:52:32 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:52:32 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:52:32 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:52:32 - startup - INFO - ============================================================
2025-07-11 23:52:32 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:52:32 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:52:32 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:52:32 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:52:32 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:52:32 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:52:32 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:52:32 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:52:32 - notification_service - INFO - Notification delivery service started
2025-07-11 23:52:32 - allora - INFO - Notification service initialized successfully
2025-07-11 23:52:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:54:16 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:54:16 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:54:16 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:54:16 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:54:16 - startup - INFO - ============================================================
2025-07-11 23:54:16 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:54:16 - startup - INFO - ============================================================
2025-07-11 23:54:16 - startup - INFO - 📅 Startup Time: 2025-07-11T23:54:16.070889
2025-07-11 23:54:16 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:54:16 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:54:16 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:54:16 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:54:16 - startup - INFO - ============================================================
2025-07-11 23:54:16 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:54:16 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.011s]
2025-07-11 23:54:16 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:54:16 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:54:16 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:54:16 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:54:16 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:54:16 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:54:16 - notification_service - INFO - Notification delivery service started
2025-07-11 23:54:16 - allora - INFO - Notification service initialized successfully
2025-07-11 23:54:16 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:55:46 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:55:46 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:55:46 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:55:46 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:55:46 - startup - INFO - ============================================================
2025-07-11 23:55:46 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:55:46 - startup - INFO - ============================================================
2025-07-11 23:55:46 - startup - INFO - 📅 Startup Time: 2025-07-11T23:55:46.290295
2025-07-11 23:55:46 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:55:46 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:55:46 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:55:46 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:55:46 - startup - INFO - ============================================================
2025-07-11 23:55:46 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:55:46 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 23:55:46 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:55:47 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:55:47 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:55:47 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:55:47 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:55:47 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:55:47 - notification_service - INFO - Notification delivery service started
2025-07-11 23:55:47 - allora - INFO - Notification service initialized successfully
2025-07-11 23:55:47 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:57:20 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:57:20 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:57:20 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:57:20 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:57:20 - startup - INFO - ============================================================
2025-07-11 23:57:20 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:57:20 - startup - INFO - ============================================================
2025-07-11 23:57:20 - startup - INFO - 📅 Startup Time: 2025-07-11T23:57:20.809023
2025-07-11 23:57:20 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:57:20 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:57:20 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:57:20 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:57:20 - startup - INFO - ============================================================
2025-07-11 23:57:20 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:57:21 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 23:57:21 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:57:21 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:57:21 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:57:21 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:57:21 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:57:21 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:57:21 - notification_service - INFO - Notification delivery service started
2025-07-11 23:57:21 - allora - INFO - Notification service initialized successfully
2025-07-11 23:57:21 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-11 23:59:00 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 23:59:00 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 23:59:00 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 23:59:00 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 23:59:00 - startup - INFO - ============================================================
2025-07-11 23:59:00 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 23:59:00 - startup - INFO - ============================================================
2025-07-11 23:59:00 - startup - INFO - 📅 Startup Time: 2025-07-11T23:59:00.868943
2025-07-11 23:59:00 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 23:59:00 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-11 23:59:00 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 23:59:00 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 23:59:00 - startup - INFO - ============================================================
2025-07-11 23:59:00 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 23:59:01 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-11 23:59:01 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 23:59:01 - allora - INFO - Search API blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 23:59:01 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 23:59:01 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 23:59:01 - tracking_system - INFO - Real-time tracking system started
2025-07-11 23:59:01 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 23:59:01 - notification_service - INFO - Notification delivery service started
2025-07-11 23:59:01 - allora - INFO - Notification service initialized successfully
2025-07-11 23:59:01 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:00:21 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 00:00:21 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 00:00:21 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 00:00:21 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 00:00:21 - startup - INFO - ============================================================
2025-07-12 00:00:21 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 00:00:21 - startup - INFO - ============================================================
2025-07-12 00:00:21 - startup - INFO - 📅 Startup Time: 2025-07-12T00:00:21.693181
2025-07-12 00:00:21 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 00:00:21 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 00:00:21 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 00:00:21 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 00:00:21 - startup - INFO - ============================================================
2025-07-12 00:00:21 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 00:00:22 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-12 00:00:22 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 00:00:22 - allora - INFO - Search API blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 00:00:22 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - Home Content API blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-12 00:00:22 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 00:00:22 - tracking_system - INFO - Real-time tracking system started
2025-07-12 00:00:22 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 00:00:22 - notification_service - INFO - Notification delivery service started
2025-07-12 00:00:22 - allora - INFO - Notification service initialized successfully
2025-07-12 00:00:22 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:14:52 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 00:14:52 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 00:14:52 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 00:14:52 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 00:14:52 - startup - INFO - ============================================================
2025-07-12 00:14:53 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 00:14:53 - startup - INFO - ============================================================
2025-07-12 00:14:53 - startup - INFO - 📅 Startup Time: 2025-07-12T00:14:53.000139
2025-07-12 00:14:53 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 00:14:53 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 00:14:53 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 00:14:53 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 00:14:53 - startup - INFO - ============================================================
2025-07-12 00:14:53 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 00:14:54 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.136s]
2025-07-12 00:14:54 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 00:14:54 - allora - INFO - Search API blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 00:14:54 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - Home Content API blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-12 00:14:54 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 00:14:54 - tracking_system - INFO - Real-time tracking system started
2025-07-12 00:14:54 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 00:14:54 - notification_service - INFO - Notification delivery service started
2025-07-12 00:14:54 - allora - INFO - Notification service initialized successfully
2025-07-12 00:14:54 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:14:55 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 00:14:55 - allora - INFO - Recommendation system initialized successfully
2025-07-12 00:14:55 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:15:46 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:16:22 - allora - ERROR - Failed to import advanced visual search model: No module named 'visual_search_model'
2025-07-12 00:16:22 - allora - ERROR - Failed to import advanced visual search model: No module named 'visual_search_model'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:16:27 - waitress.queue - WARNING - Task queue depth is 1
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:16:27 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:17:02 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:17:08 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:17:08 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:19:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:19:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting green heroes: 'Product' object has no attribute 'image_url'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 00:19:32 - sustainability_api - ERROR - Error getting sustainability metrics: 'Order' object has no attribute 'items'
2025-07-12 13:54:15 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 13:54:15 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 13:54:15 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 13:54:15 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 13:54:15 - startup - INFO - ============================================================
2025-07-12 13:54:15 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 13:54:15 - startup - INFO - ============================================================
2025-07-12 13:54:15 - startup - INFO - 📅 Startup Time: 2025-07-12T13:54:15.762014
2025-07-12 13:54:15 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 13:54:15 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 13:54:15 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 13:54:15 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 13:54:15 - startup - INFO - ============================================================
2025-07-12 13:54:15 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 13:54:16 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.243s]
2025-07-12 13:54:16 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 13:54:16 - allora - INFO - Search API blueprint registered successfully
2025-07-12 13:54:16 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 13:54:16 - allora - WARNING - Could not import user behavior API: No module named 'models.RecommendationModel'
2025-07-12 13:54:17 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 13:54:17 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 13:54:17 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 13:54:17 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 13:54:17 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 13:54:17 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-12 13:54:17 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 13:54:17 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 13:54:17 - tracking_system - INFO - Real-time tracking system started
2025-07-12 13:54:17 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 13:54:17 - notification_service - INFO - Notification delivery service started
2025-07-12 13:54:17 - allora - INFO - Notification service initialized successfully
2025-07-12 13:54:17 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 13:54:17 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 13:54:18 - allora - WARNING - Could not import recommendation API: No module named 'models.RecommendationModel'
2025-07-12 14:06:44 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 14:06:44 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 14:06:44 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 14:06:44 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 14:06:44 - startup - INFO - ============================================================
2025-07-12 14:06:44 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 14:06:44 - startup - INFO - ============================================================
2025-07-12 14:06:44 - startup - INFO - 📅 Startup Time: 2025-07-12T14:06:44.697052
2025-07-12 14:06:44 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 14:06:44 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 14:06:44 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 14:06:44 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 14:06:44 - startup - INFO - ============================================================
2025-07-12 14:06:44 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 14:06:45 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.012s]
2025-07-12 14:06:45 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 14:06:45 - allora - INFO - Search API blueprint registered successfully
2025-07-12 14:06:45 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 14:06:47 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 14:06:47 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 14:06:47 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 14:06:47 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 14:06:47 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 14:06:47 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 14:06:47 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 14:06:47 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 14:06:47 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-12 14:06:47 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 14:06:47 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 14:06:47 - tracking_system - INFO - Real-time tracking system started
2025-07-12 14:06:47 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 14:06:47 - notification_service - INFO - Notification delivery service started
2025-07-12 14:06:47 - allora - INFO - Notification service initialized successfully
2025-07-12 14:06:47 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 14:06:47 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 14:06:48 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 14:06:48 - allora - INFO - Recommendation system initialized successfully
2025-07-12 14:09:58 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 14:09:58 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 14:09:58 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 14:09:58 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 14:09:58 - startup - INFO - ============================================================
2025-07-12 14:09:58 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 14:09:58 - startup - INFO - ============================================================
2025-07-12 14:09:58 - startup - INFO - 📅 Startup Time: 2025-07-12T14:09:58.285295
2025-07-12 14:09:58 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 14:09:58 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 14:09:58 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 14:09:58 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 14:09:58 - startup - INFO - ============================================================
2025-07-12 14:09:58 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 14:09:58 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.011s]
2025-07-12 14:09:58 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 14:09:58 - allora - INFO - Search API blueprint registered successfully
2025-07-12 14:09:58 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 14:09:59 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 14:09:59 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 14:09:59 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 14:09:59 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 14:09:59 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 14:09:59 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 14:09:59 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 14:09:59 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 14:09:59 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-12 14:09:59 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 14:09:59 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 14:09:59 - tracking_system - INFO - Real-time tracking system started
2025-07-12 14:09:59 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 14:09:59 - notification_service - INFO - Notification delivery service started
2025-07-12 14:09:59 - allora - INFO - Notification service initialized successfully
2025-07-12 14:09:59 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 14:09:59 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 14:16:04 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 14:16:04 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 14:16:04 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 14:16:04 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 14:16:04 - startup - INFO - ============================================================
2025-07-12 14:16:04 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 14:16:04 - startup - INFO - ============================================================
2025-07-12 14:16:04 - startup - INFO - 📅 Startup Time: 2025-07-12T14:16:04.061406
2025-07-12 14:16:04 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 14:16:04 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 14:16:04 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 14:16:04 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 14:16:04 - startup - INFO - ============================================================
2025-07-12 14:16:04 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 14:16:04 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.009s]
2025-07-12 14:16:04 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 14:16:04 - allora - INFO - Search API blueprint registered successfully
2025-07-12 14:16:04 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 14:16:04 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 14:16:04 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 14:16:04 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 14:16:04 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 14:16:04 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 14:16:04 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 14:16:04 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 14:16:04 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 14:16:04 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 14:16:04 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 14:16:04 - tracking_system - INFO - Real-time tracking system started
2025-07-12 14:16:04 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 14:16:04 - notification_service - INFO - Notification delivery service started
2025-07-12 14:16:04 - allora - INFO - Notification service initialized successfully
2025-07-12 14:16:04 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 14:16:04 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 14:20:21 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 14:20:21 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 14:20:21 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 14:20:21 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 14:20:21 - startup - INFO - ============================================================
2025-07-12 14:20:21 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 14:20:21 - startup - INFO - ============================================================
2025-07-12 14:20:21 - startup - INFO - 📅 Startup Time: 2025-07-12T14:20:21.666644
2025-07-12 14:20:21 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 14:20:21 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 14:20:21 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 14:20:21 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 14:20:21 - startup - INFO - ============================================================
2025-07-12 14:20:21 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 14:20:22 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.010s]
2025-07-12 14:20:22 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 14:20:22 - allora - INFO - Search API blueprint registered successfully
2025-07-12 14:20:22 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 14:20:22 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 14:20:22 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 14:20:22 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 14:20:22 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 14:20:22 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 14:20:22 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 14:20:22 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 14:20:22 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 14:20:22 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 14:20:22 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 14:20:22 - allora - INFO - Recommendation system initialized successfully
2025-07-12 14:20:22 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 14:20:22 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 14:20:22 - tracking_system - INFO - Real-time tracking system started
2025-07-12 14:20:22 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 14:20:22 - notification_service - INFO - Notification delivery service started
2025-07-12 14:20:22 - allora - INFO - Notification service initialized successfully
2025-07-12 14:20:22 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 14:20:22 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 14:29:05 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 14:29:05 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 14:29:05 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 14:29:05 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 14:29:05 - startup - INFO - ============================================================
2025-07-12 14:29:05 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 14:29:05 - startup - INFO - ============================================================
2025-07-12 14:29:05 - startup - INFO - 📅 Startup Time: 2025-07-12T14:29:05.389183
2025-07-12 14:29:05 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 14:29:05 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 14:29:05 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 14:29:05 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 14:29:05 - startup - INFO - ============================================================
2025-07-12 14:29:05 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 14:29:05 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-12 14:29:05 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 14:29:05 - allora - INFO - Search API blueprint registered successfully
2025-07-12 14:29:05 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 14:29:05 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 14:29:05 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 14:29:05 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 14:29:05 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 14:29:05 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 14:29:05 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 14:29:06 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 14:29:06 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 14:29:06 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 14:29:06 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 14:29:06 - allora - INFO - Recommendation system initialized successfully
2025-07-12 14:29:06 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 14:29:06 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 14:29:06 - tracking_system - INFO - Real-time tracking system started
2025-07-12 14:29:06 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 14:29:06 - notification_service - INFO - Notification delivery service started
2025-07-12 14:29:06 - allora - INFO - Notification service initialized successfully
2025-07-12 14:29:06 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 14:29:06 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 14:43:43 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 14:43:43 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 14:43:43 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 14:43:43 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 14:43:43 - startup - INFO - ============================================================
2025-07-12 14:43:43 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 14:43:43 - startup - INFO - ============================================================
2025-07-12 14:43:43 - startup - INFO - 📅 Startup Time: 2025-07-12T14:43:43.468896
2025-07-12 14:43:43 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 14:43:43 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 14:43:43 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 14:43:43 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 14:43:43 - startup - INFO - ============================================================
2025-07-12 14:43:43 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 14:43:43 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-12 14:43:43 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 14:43:44 - allora - INFO - Search API blueprint registered successfully
2025-07-12 14:43:44 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 14:43:44 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 14:43:44 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 14:43:44 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 14:43:44 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 14:43:44 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 14:43:44 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 14:43:44 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 14:43:44 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 14:43:44 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 14:43:44 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 14:43:44 - allora - INFO - Recommendation system initialized successfully
2025-07-12 14:43:44 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 14:43:44 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 14:43:44 - tracking_system - INFO - Real-time tracking system started
2025-07-12 14:43:44 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 14:43:44 - notification_service - INFO - Notification delivery service started
2025-07-12 14:43:44 - allora - INFO - Notification service initialized successfully
2025-07-12 14:43:44 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 14:43:44 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 14:43:44 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 14:43:44 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 15:03:09 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 15:03:10 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 15:03:10 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 15:03:10 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 15:03:10 - startup - INFO - ============================================================
2025-07-12 15:03:10 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 15:03:10 - startup - INFO - ============================================================
2025-07-12 15:03:10 - startup - INFO - 📅 Startup Time: 2025-07-12T15:03:10.004560
2025-07-12 15:03:10 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 15:03:10 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 15:03:10 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 15:03:10 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 15:03:10 - startup - INFO - ============================================================
2025-07-12 15:03:10 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 15:03:10 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-12 15:03:10 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 15:03:10 - allora - INFO - Search API blueprint registered successfully
2025-07-12 15:03:10 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 15:03:10 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 15:03:10 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 15:03:10 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 15:03:10 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 15:03:10 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 15:03:10 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 15:03:10 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 15:03:10 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 15:03:10 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 15:03:10 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 15:03:10 - allora - INFO - Recommendation system initialized successfully
2025-07-12 15:03:10 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 15:03:10 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 15:03:10 - tracking_system - INFO - Real-time tracking system started
2025-07-12 15:03:10 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 15:03:10 - notification_service - INFO - Notification delivery service started
2025-07-12 15:03:10 - allora - INFO - Notification service initialized successfully
2025-07-12 15:03:10 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 15:03:10 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 15:03:12 - allora - INFO - Loading TensorFlow MobileNetV2 model for visual search...
2025-07-12 15:03:13 - allora - INFO - Successfully loaded MobileNetV2 model
2025-07-12 15:13:15 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 15:13:15 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 15:13:15 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 15:13:15 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 15:13:15 - startup - INFO - ============================================================
2025-07-12 15:13:15 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 15:13:15 - startup - INFO - ============================================================
2025-07-12 15:13:15 - startup - INFO - 📅 Startup Time: 2025-07-12T15:13:15.962018
2025-07-12 15:13:15 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 15:13:15 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 15:13:15 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 15:13:15 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 15:13:15 - startup - INFO - ============================================================
2025-07-12 15:13:15 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 15:13:16 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-12 15:13:16 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 15:13:16 - allora - INFO - Search API blueprint registered successfully
2025-07-12 15:13:16 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 15:13:16 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 15:13:16 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 15:13:16 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 15:13:16 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 15:13:16 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 15:13:16 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 15:13:16 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 15:13:16 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 15:13:16 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 15:13:16 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 15:13:16 - allora - INFO - Recommendation system initialized successfully
2025-07-12 15:13:16 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 15:13:16 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 15:13:16 - tracking_system - INFO - Real-time tracking system started
2025-07-12 15:13:16 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 15:13:16 - notification_service - INFO - Notification delivery service started
2025-07-12 15:13:16 - allora - INFO - Notification service initialized successfully
2025-07-12 15:13:16 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 15:13:16 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 15:13:21 - allora - INFO - Loading TensorFlow MobileNetV2 model for visual search...
2025-07-12 15:13:21 - allora - INFO - Successfully loaded MobileNetV2 model
2025-07-12 15:17:56 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 15:17:56 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 15:17:56 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 15:17:56 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 15:17:56 - startup - INFO - ============================================================
2025-07-12 15:17:56 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 15:17:56 - startup - INFO - ============================================================
2025-07-12 15:17:56 - startup - INFO - 📅 Startup Time: 2025-07-12T15:17:56.286986
2025-07-12 15:17:56 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 15:17:56 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 15:17:56 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 15:17:56 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 15:17:56 - startup - INFO - ============================================================
2025-07-12 15:17:56 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 15:17:56 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.009s]
2025-07-12 15:17:56 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 15:17:57 - allora - INFO - Search API blueprint registered successfully
2025-07-12 15:17:57 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 15:17:57 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 15:17:57 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 15:17:57 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 15:17:57 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 15:17:57 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 15:17:57 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 15:17:57 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 15:17:57 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 15:17:57 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 15:17:57 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 15:17:57 - allora - INFO - Recommendation system initialized successfully
2025-07-12 15:17:57 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 15:17:57 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 15:17:57 - tracking_system - INFO - Real-time tracking system started
2025-07-12 15:17:57 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 15:17:57 - notification_service - INFO - Notification delivery service started
2025-07-12 15:17:57 - allora - INFO - Notification service initialized successfully
2025-07-12 15:17:57 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 15:17:57 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 15:24:26 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 15:24:26 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 15:24:26 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 15:24:26 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 15:24:26 - startup - INFO - ============================================================
2025-07-12 15:24:26 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 15:24:26 - startup - INFO - ============================================================
2025-07-12 15:24:26 - startup - INFO - 📅 Startup Time: 2025-07-12T15:24:26.449456
2025-07-12 15:24:26 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 15:24:26 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 15:24:26 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 15:24:26 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 15:24:26 - startup - INFO - ============================================================
2025-07-12 15:24:26 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 15:24:26 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-12 15:24:26 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 15:24:26 - allora - INFO - Search API blueprint registered successfully
2025-07-12 15:24:26 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 15:24:26 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 15:24:26 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 15:24:26 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 15:24:27 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 15:24:27 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 15:24:27 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 15:24:27 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 15:24:27 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 15:24:27 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 15:24:27 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 15:24:27 - allora - INFO - Recommendation system initialized successfully
2025-07-12 15:24:27 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 15:24:27 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 15:24:27 - tracking_system - INFO - Real-time tracking system started
2025-07-12 15:24:27 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 15:24:27 - notification_service - INFO - Notification delivery service started
2025-07-12 15:24:27 - allora - INFO - Notification service initialized successfully
2025-07-12 15:24:27 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 15:24:27 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 15:46:35 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 15:46:35 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 15:46:35 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 15:46:35 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 15:46:35 - startup - INFO - ============================================================
2025-07-12 15:46:35 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 15:46:35 - startup - INFO - ============================================================
2025-07-12 15:46:35 - startup - INFO - 📅 Startup Time: 2025-07-12T15:46:35.358846
2025-07-12 15:46:35 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 15:46:35 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 15:46:35 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 15:46:35 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 15:46:35 - startup - INFO - ============================================================
2025-07-12 15:46:35 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 15:46:35 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-12 15:46:35 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 15:46:35 - allora - INFO - Search API blueprint registered successfully
2025-07-12 15:46:35 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 15:46:35 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 15:46:35 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 15:46:35 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 15:46:35 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 15:46:35 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 15:46:35 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 15:46:35 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 15:46:35 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 15:46:35 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 15:46:35 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 15:46:35 - allora - INFO - Recommendation system initialized successfully
2025-07-12 15:46:35 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 15:46:35 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 15:46:35 - tracking_system - INFO - Real-time tracking system started
2025-07-12 15:46:35 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 15:46:35 - notification_service - INFO - Notification delivery service started
2025-07-12 15:46:35 - allora - INFO - Notification service initialized successfully
2025-07-12 15:46:35 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 15:46:35 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:00:25 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:00:25 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:00:25 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:00:25 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:00:25 - startup - INFO - ============================================================
2025-07-12 16:00:25 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:00:25 - startup - INFO - ============================================================
2025-07-12 16:00:25 - startup - INFO - 📅 Startup Time: 2025-07-12T16:00:25.999322
2025-07-12 16:00:25 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:00:25 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:00:26 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:00:26 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:00:26 - startup - INFO - ============================================================
2025-07-12 16:00:26 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:00:26 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.010s]
2025-07-12 16:00:26 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:00:26 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:00:26 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:00:26 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:00:26 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:00:26 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:00:26 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:00:26 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:00:26 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:00:26 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:00:26 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:00:26 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:00:26 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:00:26 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:00:26 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:00:26 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:00:26 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:00:26 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:00:26 - notification_service - INFO - Notification delivery service started
2025-07-12 16:00:26 - allora - INFO - Notification service initialized successfully
2025-07-12 16:00:26 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:00:26 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:04:52 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:04:52 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:04:52 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:04:52 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:04:52 - startup - INFO - ============================================================
2025-07-12 16:04:52 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:04:52 - startup - INFO - ============================================================
2025-07-12 16:04:52 - startup - INFO - 📅 Startup Time: 2025-07-12T16:04:52.589758
2025-07-12 16:04:52 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:04:52 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:04:52 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:04:52 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:04:52 - startup - INFO - ============================================================
2025-07-12 16:04:52 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:04:52 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-12 16:04:52 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:04:52 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:04:52 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:04:53 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:04:53 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:04:53 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:04:53 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:04:53 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:04:53 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:04:53 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:04:53 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:04:53 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:04:53 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:04:53 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:04:53 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:04:53 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:04:53 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:04:53 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:04:53 - notification_service - INFO - Notification delivery service started
2025-07-12 16:04:53 - allora - INFO - Notification service initialized successfully
2025-07-12 16:04:53 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:04:53 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:18:59 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:18:59 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:18:59 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:18:59 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:18:59 - startup - INFO - ============================================================
2025-07-12 16:18:59 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:18:59 - startup - INFO - ============================================================
2025-07-12 16:18:59 - startup - INFO - 📅 Startup Time: 2025-07-12T16:18:59.298747
2025-07-12 16:18:59 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:18:59 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:18:59 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:18:59 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:18:59 - startup - INFO - ============================================================
2025-07-12 16:18:59 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:18:59 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-12 16:18:59 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:18:59 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:18:59 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:18:59 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:18:59 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:18:59 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:18:59 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:18:59 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:18:59 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:18:59 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:18:59 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:18:59 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:18:59 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:18:59 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:18:59 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:18:59 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:18:59 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:18:59 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:18:59 - notification_service - INFO - Notification delivery service started
2025-07-12 16:18:59 - allora - INFO - Notification service initialized successfully
2025-07-12 16:19:00 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:19:00 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:23:48 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:23:48 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:23:48 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:23:48 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:23:48 - startup - INFO - ============================================================
2025-07-12 16:23:48 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:23:48 - startup - INFO - ============================================================
2025-07-12 16:23:48 - startup - INFO - 📅 Startup Time: 2025-07-12T16:23:48.817517
2025-07-12 16:23:48 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:23:48 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:23:48 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:23:48 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:23:48 - startup - INFO - ============================================================
2025-07-12 16:23:48 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:23:53 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.048s]
2025-07-12 16:23:53 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-07-12 16:23:53 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 0 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001FA5070DC60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001FA5070DC60>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-12 16:23:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.054s]
2025-07-12 16:23:57 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 2 times in a row, putting on 2 second timeout
2025-07-12 16:23:57 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 1 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001FA5070E0E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001FA5070E0E0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-12 16:23:57 - elastic_transport.node_pool - INFO - Resurrected node <Urllib3HttpNode(http://localhost:9200)> (force=False)
2025-07-12 16:24:01 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.069s]
2025-07-12 16:24:01 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 3 times in a row, putting on 4 second timeout
2025-07-12 16:24:01 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 2 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001FA5070E260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001FA5070E260>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-12 16:24:01 - elastic_transport.node_pool - INFO - Resurrected node <Urllib3HttpNode(http://localhost:9200)> (force=False)
2025-07-12 16:24:05 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.034s]
2025-07-12 16:24:05 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 4 times in a row, putting on 8 second timeout
2025-07-12 16:24:05 - elasticsearch_config - WARNING - Failed to connect to Elasticsearch - running without search functionality
2025-07-12 16:24:05 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:24:05 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:24:06 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:24:06 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:24:06 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:24:06 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:24:06 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:24:06 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:24:06 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:24:06 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:24:06 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:24:06 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:24:06 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:24:06 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:24:06 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:24:06 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:24:06 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:24:06 - notification_service - INFO - Notification delivery service started
2025-07-12 16:24:06 - allora - INFO - Notification service initialized successfully
2025-07-12 16:24:06 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:24:06 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:34:12 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:34:12 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:34:12 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:34:12 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:34:12 - startup - INFO - ============================================================
2025-07-12 16:34:12 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:34:12 - startup - INFO - ============================================================
2025-07-12 16:34:12 - startup - INFO - 📅 Startup Time: 2025-07-12T16:34:12.380427
2025-07-12 16:34:12 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:34:12 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:34:12 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:34:12 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:34:12 - startup - INFO - ============================================================
2025-07-12 16:34:12 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:34:13 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.173s]
2025-07-12 16:34:13 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:34:13 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:34:13 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:34:14 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:34:14 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:34:14 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:34:14 - startup - INFO - ============================================================
2025-07-12 16:34:14 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:34:14 - startup - INFO - ============================================================
2025-07-12 16:34:14 - startup - INFO - 📅 Startup Time: 2025-07-12T16:34:14.317403
2025-07-12 16:34:14 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:34:14 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:34:14 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:34:14 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:34:14 - startup - INFO - ============================================================
2025-07-12 16:34:14 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:34:14 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:34:14 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:34:14 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:34:14 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:34:14 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:34:14 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:34:14 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:34:14 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:34:14 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:34:14 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:34:14 - notification_service - INFO - Notification delivery service started
2025-07-12 16:34:14 - allora - INFO - Notification service initialized successfully
2025-07-12 16:34:14 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:34:14 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:34:15 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:34:15 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:34:15 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:34:15 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:34:15 - allora - INFO - Notification service initialized successfully
2025-07-12 16:34:15 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:34:15 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:34:15 - scheduler_init - INFO - Inventory scheduler temporarily disabled to avoid circular imports
2025-07-12 16:34:15 - scheduler_init - INFO - Scheduler initialization will start in 10 seconds
2025-07-12 16:34:15 - scheduler_init - INFO - Scheduler initialization process started
2025-07-12 16:34:15 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-12 16:34:15 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 16:34:15 - werkzeug - INFO -  * Restarting with stat
2025-07-12 16:34:24 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:34:24 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:34:24 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:34:24 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:34:24 - startup - INFO - ============================================================
2025-07-12 16:34:24 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:34:24 - startup - INFO - ============================================================
2025-07-12 16:34:24 - startup - INFO - 📅 Startup Time: 2025-07-12T16:34:24.826552
2025-07-12 16:34:24 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:34:24 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:34:24 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:34:24 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:34:24 - startup - INFO - ============================================================
2025-07-12 16:34:24 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:34:25 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.014s]
2025-07-12 16:34:25 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:34:25 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:34:25 - scheduler_init - INFO - Starting scheduler initialization...
2025-07-12 16:34:25 - scheduler_init - INFO - Scheduler initialization completed
2025-07-12 16:34:25 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:34:25 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:34:25 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:34:25 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:34:25 - startup - INFO - ============================================================
2025-07-12 16:34:25 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:34:25 - startup - INFO - ============================================================
2025-07-12 16:34:25 - startup - INFO - 📅 Startup Time: 2025-07-12T16:34:25.819322
2025-07-12 16:34:25 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:34:25 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:34:25 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:34:25 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:34:25 - startup - INFO - ============================================================
2025-07-12 16:34:25 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:34:25 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:34:25 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:34:25 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:34:25 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:34:25 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:34:25 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:34:25 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:34:25 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:34:25 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:34:25 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:34:25 - notification_service - INFO - Notification delivery service started
2025-07-12 16:34:25 - allora - INFO - Notification service initialized successfully
2025-07-12 16:34:25 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:34:25 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:34:26 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:34:26 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:34:26 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:34:26 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:34:26 - allora - INFO - Notification service initialized successfully
2025-07-12 16:34:26 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:34:26 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:34:26 - scheduler_init - INFO - Inventory scheduler temporarily disabled to avoid circular imports
2025-07-12 16:34:26 - scheduler_init - INFO - Scheduler initialization will start in 10 seconds
2025-07-12 16:34:26 - scheduler_init - INFO - Scheduler initialization process started
2025-07-12 16:34:26 - werkzeug - WARNING -  * Debugger is active!
2025-07-12 16:34:26 - werkzeug - INFO -  * Debugger PIN: 808-315-739
2025-07-12 16:34:36 - scheduler_init - INFO - Starting scheduler initialization...
2025-07-12 16:34:36 - scheduler_init - INFO - Scheduler initialization completed
2025-07-12 16:38:03 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:38:03 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:38:03 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:38:03 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:38:03 - startup - INFO - ============================================================
2025-07-12 16:38:03 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:38:03 - startup - INFO - ============================================================
2025-07-12 16:38:03 - startup - INFO - 📅 Startup Time: 2025-07-12T16:38:03.420493
2025-07-12 16:38:03 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:38:03 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:38:03 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:38:03 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:38:03 - startup - INFO - ============================================================
2025-07-12 16:38:03 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:38:03 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.009s]
2025-07-12 16:38:03 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:38:03 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:38:03 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:38:04 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:38:04 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:38:04 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:38:04 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:38:04 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:38:04 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:38:04 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:38:04 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:38:04 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:38:04 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:38:04 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:38:04 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:38:04 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:38:04 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:38:04 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:38:04 - notification_service - INFO - Notification delivery service started
2025-07-12 16:38:04 - allora - INFO - Notification service initialized successfully
2025-07-12 16:38:04 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:38:04 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:38:32 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:38:32 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:38:32 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:38:32 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:38:32 - startup - INFO - ============================================================
2025-07-12 16:38:32 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:38:32 - startup - INFO - ============================================================
2025-07-12 16:38:32 - startup - INFO - 📅 Startup Time: 2025-07-12T16:38:32.967955
2025-07-12 16:38:32 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:38:32 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:38:32 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:38:32 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:38:32 - startup - INFO - ============================================================
2025-07-12 16:38:32 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:38:33 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-12 16:38:33 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:38:33 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:38:33 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:38:33 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:38:33 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:38:33 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:38:33 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:38:33 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:38:33 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:38:33 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:38:33 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:38:33 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:38:33 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:38:33 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:38:33 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:38:33 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:38:33 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:38:33 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:38:33 - notification_service - INFO - Notification delivery service started
2025-07-12 16:38:33 - allora - INFO - Notification service initialized successfully
2025-07-12 16:38:33 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:38:33 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:39:03 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:39:03 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:39:03 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:39:03 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:39:03 - startup - INFO - ============================================================
2025-07-12 16:39:03 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:39:03 - startup - INFO - ============================================================
2025-07-12 16:39:03 - startup - INFO - 📅 Startup Time: 2025-07-12T16:39:03.260039
2025-07-12 16:39:03 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:39:03 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:39:03 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:39:03 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:39:03 - startup - INFO - ============================================================
2025-07-12 16:39:03 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:39:03 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.010s]
2025-07-12 16:39:03 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:39:03 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:39:03 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:39:03 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:39:03 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:39:03 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:39:04 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:39:04 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:39:04 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:39:04 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:39:04 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:39:04 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:39:04 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:39:04 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:39:04 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:39:04 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:39:04 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:39:04 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:39:04 - notification_service - INFO - Notification delivery service started
2025-07-12 16:39:04 - allora - INFO - Notification service initialized successfully
2025-07-12 16:39:04 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:39:04 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:40:42 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:40:42 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:40:42 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:40:42 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:40:42 - startup - INFO - ============================================================
2025-07-12 16:40:42 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:40:42 - startup - INFO - ============================================================
2025-07-12 16:40:42 - startup - INFO - 📅 Startup Time: 2025-07-12T16:40:42.295827
2025-07-12 16:40:42 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:40:42 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:40:42 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:40:42 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:40:42 - startup - INFO - ============================================================
2025-07-12 16:40:42 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:40:42 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.017s]
2025-07-12 16:40:42 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:40:42 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:40:42 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:40:42 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:40:42 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:40:42 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:40:42 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:40:42 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:40:42 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:40:42 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:40:42 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:40:42 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:40:42 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:40:42 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:40:42 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:40:42 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:40:42 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:40:42 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:40:42 - notification_service - INFO - Notification delivery service started
2025-07-12 16:40:42 - allora - INFO - Notification service initialized successfully
2025-07-12 16:40:42 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:40:42 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:40:44 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-12 16:40:44 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 16:43:11 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:43:11 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:43:11 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:43:11 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:43:11 - startup - INFO - ============================================================
2025-07-12 16:43:11 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:43:11 - startup - INFO - ============================================================
2025-07-12 16:43:11 - startup - INFO - 📅 Startup Time: 2025-07-12T16:43:11.359940
2025-07-12 16:43:11 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:43:11 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:43:11 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:43:11 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:43:11 - startup - INFO - ============================================================
2025-07-12 16:43:11 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:43:11 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-12 16:43:11 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:43:11 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:43:11 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:43:12 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:43:12 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:43:12 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:43:12 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:43:12 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:43:12 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:43:12 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:43:12 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:43:12 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:43:12 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:43:12 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:43:12 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:43:12 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:43:12 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:43:12 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:43:12 - notification_service - INFO - Notification delivery service started
2025-07-12 16:43:12 - allora - INFO - Notification service initialized successfully
2025-07-12 16:43:12 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:43:12 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:43:12 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-12 16:43:12 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 16:44:32 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:44:32 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:44:32 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:44:32 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:44:32 - startup - INFO - ============================================================
2025-07-12 16:44:32 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:44:32 - startup - INFO - ============================================================
2025-07-12 16:44:32 - startup - INFO - 📅 Startup Time: 2025-07-12T16:44:32.156332
2025-07-12 16:44:32 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:44:32 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:44:32 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:44:32 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:44:32 - startup - INFO - ============================================================
2025-07-12 16:44:32 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:44:32 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-12 16:44:32 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:44:32 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:44:32 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:44:32 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:44:32 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:44:32 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:44:32 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:44:32 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:44:32 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:44:32 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:44:32 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:44:32 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:44:33 - startup - INFO - ============================================================
2025-07-12 16:44:33 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:44:33 - startup - INFO - ============================================================
2025-07-12 16:44:33 - startup - INFO - 📅 Startup Time: 2025-07-12T16:44:33.176986
2025-07-12 16:44:33 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:44:33 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:44:33 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:44:33 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:44:33 - startup - INFO - ============================================================
2025-07-12 16:44:33 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:44:33 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:44:33 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:44:33 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:44:33 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:44:33 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:44:33 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:44:33 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:44:33 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:44:33 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:44:33 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:44:33 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:44:33 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:44:33 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:44:33 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:44:33 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:44:33 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:44:33 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:44:33 - notification_service - INFO - Notification delivery service started
2025-07-12 16:44:33 - allora - INFO - Notification service initialized successfully
2025-07-12 16:44:33 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:44:33 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:44:33 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:44:33 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:44:33 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:44:33 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:44:33 - allora - INFO - Notification service initialized successfully
2025-07-12 16:44:33 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:44:33 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:44:34 - scheduler_init - INFO - Inventory scheduler temporarily disabled to avoid circular imports
2025-07-12 16:44:34 - scheduler_init - INFO - Scheduler initialization will start in 10 seconds
2025-07-12 16:44:34 - scheduler_init - INFO - Scheduler initialization process started
2025-07-12 16:44:34 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-12 16:44:34 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-12 16:44:34 - werkzeug - INFO -  * Restarting with stat
2025-07-12 16:44:42 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:44:42 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:44:42 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:44:42 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:44:42 - startup - INFO - ============================================================
2025-07-12 16:44:42 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:44:42 - startup - INFO - ============================================================
2025-07-12 16:44:42 - startup - INFO - 📅 Startup Time: 2025-07-12T16:44:42.899304
2025-07-12 16:44:42 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:44:42 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:44:42 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:44:42 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:44:42 - startup - INFO - ============================================================
2025-07-12 16:44:42 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:44:43 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-12 16:44:43 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:44:43 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:44:43 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:44:43 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:44:43 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:44:43 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:44:43 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:44:43 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:44:43 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:44:43 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:44:43 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:44:43 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:44:43 - startup - INFO - ============================================================
2025-07-12 16:44:43 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:44:43 - startup - INFO - ============================================================
2025-07-12 16:44:44 - startup - INFO - 📅 Startup Time: 2025-07-12T16:44:44.000665
2025-07-12 16:44:44 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:44:44 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:44:44 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:44:44 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:44:44 - startup - INFO - ============================================================
2025-07-12 16:44:44 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:44:44 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:44:44 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:44:44 - scheduler_init - INFO - Starting scheduler initialization...
2025-07-12 16:44:44 - scheduler_init - INFO - Scheduler initialization completed
2025-07-12 16:44:44 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:44:44 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:44:44 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:44:44 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:44:44 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:44:44 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:44:44 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:44:44 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:44:44 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:44:44 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:44:44 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:44:44 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:44:44 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:44:44 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:44:44 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:44:44 - notification_service - INFO - Notification delivery service started
2025-07-12 16:44:44 - allora - INFO - Notification service initialized successfully
2025-07-12 16:44:44 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:44:44 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:44:44 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:44:44 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:44:44 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:44:44 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:44:44 - allora - INFO - Notification service initialized successfully
2025-07-12 16:44:44 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:44:44 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:44:44 - scheduler_init - INFO - Inventory scheduler temporarily disabled to avoid circular imports
2025-07-12 16:44:44 - scheduler_init - INFO - Scheduler initialization will start in 10 seconds
2025-07-12 16:44:44 - scheduler_init - INFO - Scheduler initialization process started
2025-07-12 16:44:44 - werkzeug - WARNING -  * Debugger is active!
2025-07-12 16:44:44 - werkzeug - INFO -  * Debugger PIN: 808-315-739
2025-07-12 16:44:45 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:44:45] "[33mGET / HTTP/1.1[0m" 404 -
2025-07-12 16:44:45 - werkzeug - INFO - 127.0.0.1 - - [12/Jul/2025 16:44:45] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-12 16:44:54 - scheduler_init - INFO - Starting scheduler initialization...
2025-07-12 16:44:54 - scheduler_init - INFO - Scheduler initialization completed
2025-07-12 16:47:15 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:47:15 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:47:15 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:47:15 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:47:15 - startup - INFO - ============================================================
2025-07-12 16:47:15 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:47:15 - startup - INFO - ============================================================
2025-07-12 16:47:15 - startup - INFO - 📅 Startup Time: 2025-07-12T16:47:15.700852
2025-07-12 16:47:15 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:47:15 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:47:15 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:47:15 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:47:15 - startup - INFO - ============================================================
2025-07-12 16:47:15 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:47:16 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-12 16:47:16 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:47:16 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:47:16 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:47:16 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:47:16 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:47:16 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:47:16 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:47:16 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:47:16 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:47:16 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:47:16 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:47:16 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:47:16 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:47:16 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:47:16 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:47:16 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:47:16 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:47:16 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:47:16 - notification_service - INFO - Notification delivery service started
2025-07-12 16:47:16 - allora - INFO - Notification service initialized successfully
2025-07-12 16:47:16 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:47:16 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:48:42 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:48:42 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:48:42 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:48:42 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:48:42 - startup - INFO - ============================================================
2025-07-12 16:48:42 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:48:42 - startup - INFO - ============================================================
2025-07-12 16:48:42 - startup - INFO - 📅 Startup Time: 2025-07-12T16:48:42.692743
2025-07-12 16:48:42 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:48:42 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:48:42 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:48:42 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:48:42 - startup - INFO - ============================================================
2025-07-12 16:48:42 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:48:43 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-12 16:48:43 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:48:43 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:48:43 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:48:43 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:48:43 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:48:43 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:48:43 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:48:43 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:48:43 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:48:43 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:48:43 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:48:43 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:48:43 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:48:43 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:48:43 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:48:43 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:48:43 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:48:43 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:48:43 - notification_service - INFO - Notification delivery service started
2025-07-12 16:48:43 - allora - INFO - Notification service initialized successfully
2025-07-12 16:48:43 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:48:43 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:48:44 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-12 16:50:55 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:50:55 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:50:55 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:50:55 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:50:55 - startup - INFO - ============================================================
2025-07-12 16:50:55 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:50:55 - startup - INFO - ============================================================
2025-07-12 16:50:55 - startup - INFO - 📅 Startup Time: 2025-07-12T16:50:55.585609
2025-07-12 16:50:55 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:50:55 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:50:55 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:50:55 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:50:55 - startup - INFO - ============================================================
2025-07-12 16:50:55 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:50:55 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-12 16:50:55 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:50:56 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:50:56 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:50:56 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:50:56 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:50:56 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:50:56 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:50:56 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:50:56 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:50:56 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:50:56 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:50:56 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:50:56 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:50:56 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:50:56 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:50:56 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:50:56 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:50:56 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:50:56 - notification_service - INFO - Notification delivery service started
2025-07-12 16:50:56 - allora - INFO - Notification service initialized successfully
2025-07-12 16:50:56 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:50:56 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:51:19 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:51:19 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:51:19 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:51:19 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:51:19 - startup - INFO - ============================================================
2025-07-12 16:51:19 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:51:19 - startup - INFO - ============================================================
2025-07-12 16:51:19 - startup - INFO - 📅 Startup Time: 2025-07-12T16:51:19.928989
2025-07-12 16:51:19 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:51:19 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:51:19 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:51:19 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:51:19 - startup - INFO - ============================================================
2025-07-12 16:51:19 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:51:20 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-12 16:51:20 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:51:20 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:51:20 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:51:20 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:51:20 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:51:20 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:51:20 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:51:20 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:51:20 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:51:20 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:51:20 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:51:20 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:51:20 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:51:20 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:51:20 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:51:20 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:51:20 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:51:20 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:51:20 - notification_service - INFO - Notification delivery service started
2025-07-12 16:51:20 - allora - INFO - Notification service initialized successfully
2025-07-12 16:51:20 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:51:20 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:53:13 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:53:13 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:53:13 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:53:13 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:53:13 - startup - INFO - ============================================================
2025-07-12 16:53:13 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:53:13 - startup - INFO - ============================================================
2025-07-12 16:53:13 - startup - INFO - 📅 Startup Time: 2025-07-12T16:53:13.369433
2025-07-12 16:53:13 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:53:13 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:53:13 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:53:13 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:53:13 - startup - INFO - ============================================================
2025-07-12 16:53:13 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:53:13 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.009s]
2025-07-12 16:53:13 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:53:13 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:53:13 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:53:14 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:53:14 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:53:14 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:53:14 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:53:14 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:53:14 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:53:14 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:53:14 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:53:14 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:53:14 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:53:14 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:53:14 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:53:14 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:53:14 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:53:14 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:53:14 - notification_service - INFO - Notification delivery service started
2025-07-12 16:53:14 - allora - INFO - Notification service initialized successfully
2025-07-12 16:53:14 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:53:14 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:54:06 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:54:06 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:54:06 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:54:06 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:54:06 - startup - INFO - ============================================================
2025-07-12 16:54:06 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:54:06 - startup - INFO - ============================================================
2025-07-12 16:54:06 - startup - INFO - 📅 Startup Time: 2025-07-12T16:54:06.295296
2025-07-12 16:54:06 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:54:06 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:54:06 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:54:06 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:54:06 - startup - INFO - ============================================================
2025-07-12 16:54:06 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:54:06 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-12 16:54:06 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:54:06 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:54:06 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:54:06 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:54:06 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:54:06 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:54:07 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:54:07 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:54:07 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:54:07 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:54:07 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:54:07 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:54:07 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:54:07 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:54:07 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:54:07 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:54:07 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:54:07 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:54:07 - notification_service - INFO - Notification delivery service started
2025-07-12 16:54:07 - allora - INFO - Notification service initialized successfully
2025-07-12 16:54:07 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:54:07 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:54:08 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-12 16:54:53 - allora - WARNING - 404 error for path: /favicon.ico
2025-07-12 16:57:47 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.204s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.013s]
2025-07-12 16:57:47 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 16:57:47 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_products [status:200 duration:0.021s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.041s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_products/_stats [status:200 duration:0.025s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_products [status:200 duration:0.031s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_search_analytics [status:200 duration:0.083s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_search_analytics/_stats [status:200 duration:0.009s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_search_analytics [status:200 duration:0.007s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_suggestions [status:200 duration:0.003s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_suggestions/_stats [status:200 duration:0.005s]
2025-07-12 16:57:47 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_suggestions [status:200 duration:0.005s]
2025-07-12 16:57:48 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 16:57:48 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 16:58:38 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 16:58:38 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 16:58:38 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 16:58:38 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 16:58:38 - startup - INFO - ============================================================
2025-07-12 16:58:38 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 16:58:38 - startup - INFO - ============================================================
2025-07-12 16:58:38 - startup - INFO - 📅 Startup Time: 2025-07-12T16:58:38.***********-07-12 16:58:38 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 16:58:38 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 16:58:38 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 16:58:38 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 16:58:38 - startup - INFO - ============================================================
2025-07-12 16:58:38 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 16:58:38 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-12 16:58:38 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 16:58:38 - allora - INFO - Search API blueprint registered successfully
2025-07-12 16:58:38 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 16:58:38 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 16:58:38 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 16:58:38 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 16:58:39 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 16:58:39 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 16:58:39 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 16:58:39 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 16:58:39 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 16:58:39 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 16:58:39 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 16:58:39 - allora - INFO - Recommendation system initialized successfully
2025-07-12 16:58:39 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 16:58:39 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 16:58:39 - tracking_system - INFO - Real-time tracking system started
2025-07-12 16:58:39 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 16:58:39 - notification_service - INFO - Notification delivery service started
2025-07-12 16:58:39 - allora - INFO - Notification service initialized successfully
2025-07-12 16:58:39 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 16:58:39 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 16:58:40 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-12 16:59:12 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.005s]
2025-07-12 17:00:00 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.005s]
2025-07-12 17:02:04 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.007s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.014s]
2025-07-12 17:02:06 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:02:06 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:02:06 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.006s]
2025-07-12 17:02:06 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:02:06 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.007s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.003s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_products [status:200 duration:0.003s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_products/_stats [status:200 duration:0.005s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_products [status:200 duration:0.004s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_search_analytics [status:200 duration:0.003s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_search_analytics/_stats [status:200 duration:0.004s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_search_analytics [status:200 duration:0.003s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_suggestions [status:200 duration:0.004s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_suggestions/_stats [status:200 duration:0.005s]
2025-07-12 17:02:06 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_suggestions [status:200 duration:0.002s]
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:02:08 - allora - ERROR - Frontend Error: 
2025-07-12 17:02:08 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:51 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.006s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.005s]
2025-07-12 17:06:51 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:06:51 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:06:51 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 17:06:51 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:06:51 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.003s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_products [status:200 duration:0.004s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_products/_stats [status:200 duration:0.005s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_products [status:200 duration:0.002s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_search_analytics [status:200 duration:0.004s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_search_analytics/_stats [status:200 duration:0.003s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_search_analytics [status:200 duration:0.003s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_suggestions [status:200 duration:0.003s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_suggestions/_stats [status:200 duration:0.005s]
2025-07-12 17:06:51 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_suggestions [status:200 duration:0.004s]
2025-07-12 17:06:52 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:06:52 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:06:52 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:53 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:06:54 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:06:54 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:06:54 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    user, error_response = verify_token()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:54 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    return jsonify({'error': 'Missing required refund information'}), 400
ValueError: too many values to unpack (expected 2)
2025-07-12 17:06:54 - allora - ERROR - Frontend Error: 
2025-07-12 17:06:54 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
    }
ValueError: too many values to unpack (expected 2)
2025-07-12 17:09:54 - allora - WARNING - 404 error for path: /api/
2025-07-12 17:15:51 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.006s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.005s]
2025-07-12 17:15:51 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:15:51 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:15:51 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 17:15:51 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:15:51 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.002s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_products [status:200 duration:0.003s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_products/_stats [status:200 duration:0.003s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_products [status:200 duration:0.003s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_search_analytics [status:200 duration:0.002s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_search_analytics/_stats [status:200 duration:0.002s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_search_analytics [status:200 duration:0.004s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_suggestions [status:200 duration:0.002s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_suggestions/_stats [status:200 duration:0.003s]
2025-07-12 17:15:51 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_suggestions [status:200 duration:0.002s]
2025-07-12 17:15:52 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:15:52 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    def addresses():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    def addresses():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    def payment_methods():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    def recently_viewed():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    def cart():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    def cart():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    """Get smart bundle recommendations based on current cart contents"""
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def orders():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def orders():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    def order_detail(order_id):
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    def wishlist():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    def wishlist():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    if request.method == 'POST':
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    order = Order.query.filter_by(id=order_id, user_id=user.id).first_or_404()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
    except Exception as e:
ValueError: too many values to unpack (expected 2)
2025-07-12 17:15:53 - allora - ERROR - Frontend Error: 
2025-07-12 17:16:38 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:16:38 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    def cart():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:18:59 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 17:18:59 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 17:18:59 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 17:18:59 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 17:18:59 - startup - INFO - ============================================================
2025-07-12 17:18:59 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 17:18:59 - startup - INFO - ============================================================
2025-07-12 17:18:59 - startup - INFO - 📅 Startup Time: 2025-07-12T17:18:59.478122
2025-07-12 17:18:59 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 17:18:59 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 17:18:59 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 17:18:59 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 17:18:59 - startup - INFO - ============================================================
2025-07-12 17:18:59 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 17:18:59 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-12 17:18:59 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 17:18:59 - allora - INFO - Search API blueprint registered successfully
2025-07-12 17:18:59 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 17:19:00 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 17:19:00 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 17:19:00 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 17:19:00 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 17:19:00 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 17:19:00 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 17:19:00 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 17:19:00 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 17:19:00 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 17:19:00 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 17:19:00 - allora - INFO - Recommendation system initialized successfully
2025-07-12 17:19:00 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 17:19:00 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 17:19:00 - tracking_system - INFO - Real-time tracking system started
2025-07-12 17:19:00 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 17:19:00 - notification_service - INFO - Notification delivery service started
2025-07-12 17:19:00 - allora - INFO - Notification service initialized successfully
2025-07-12 17:19:00 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 17:19:00 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 17:20:16 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 17:20:16 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 17:20:16 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 17:20:16 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 17:20:16 - startup - INFO - ============================================================
2025-07-12 17:20:16 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 17:20:16 - startup - INFO - ============================================================
2025-07-12 17:20:16 - startup - INFO - 📅 Startup Time: 2025-07-12T17:20:16.502502
2025-07-12 17:20:16 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 17:20:16 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 17:20:16 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 17:20:16 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 17:20:16 - startup - INFO - ============================================================
2025-07-12 17:20:16 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 17:20:16 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-12 17:20:16 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 17:20:16 - allora - INFO - Search API blueprint registered successfully
2025-07-12 17:20:16 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 17:20:17 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 17:20:17 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 17:20:17 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 17:20:17 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 17:20:17 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 17:20:17 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 17:20:17 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 17:20:17 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 17:20:17 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 17:20:17 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 17:20:17 - allora - INFO - Recommendation system initialized successfully
2025-07-12 17:20:17 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 17:20:17 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 17:20:17 - tracking_system - INFO - Real-time tracking system started
2025-07-12 17:20:17 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 17:20:17 - notification_service - INFO - Notification delivery service started
2025-07-12 17:20:17 - allora - INFO - Notification service initialized successfully
2025-07-12 17:20:17 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 17:20:17 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 17:23:57 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.006s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 17:23:57 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:23:57 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:23:57 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 17:23:57 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:23:57 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.005s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.002s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_products [status:200 duration:0.003s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_products/_stats [status:200 duration:0.004s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_products [status:200 duration:0.003s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_search_analytics [status:200 duration:0.004s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_search_analytics/_stats [status:200 duration:0.003s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_search_analytics [status:200 duration:0.003s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_suggestions [status:200 duration:0.002s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_suggestions/_stats [status:200 duration:0.004s]
2025-07-12 17:23:57 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_suggestions [status:200 duration:0.002s]
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    def addresses():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    def addresses():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    })
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    return jsonify({
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    return jsonify({'message': 'Payment method deleted successfully'})
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    return jsonify({'message': 'Payment method deleted successfully'})
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    db.session.commit()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    # Order Management Endpoints
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    # Order Management Endpoints
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    db.session.rollback()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    def wishlist():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    def wishlist():
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    if request.method == 'POST':
ValueError: too many values to unpack (expected 2)
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:23:59 - allora - ERROR - Frontend Error: 
2025-07-12 17:23:59 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
    'permissions': {
ValueError: too many values to unpack (expected 2)
2025-07-12 17:24:00 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    order_id = data.get('order_id')
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:04 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.006s]
2025-07-12 17:28:04 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.005s]
2025-07-12 17:28:04 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:28:04 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:28:05 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.002s]
2025-07-12 17:28:05 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:28:05 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 17:28:05 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.002s]
2025-07-12 17:28:05 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_products [status:200 duration:0.003s]
2025-07-12 17:28:05 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_products/_stats [status:200 duration:0.004s]
2025-07-12 17:28:05 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_products [status:200 duration:0.002s]
2025-07-12 17:28:05 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_search_analytics [status:200 duration:0.003s]
2025-07-12 17:28:05 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_search_analytics/_stats [status:200 duration:0.005s]
2025-07-12 17:28:05 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_search_analytics [status:200 duration:0.003s]
2025-07-12 17:28:05 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_suggestions [status:200 duration:0.004s]
2025-07-12 17:28:05 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_suggestions/_stats [status:200 duration:0.003s]
2025-07-12 17:28:05 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_suggestions [status:200 duration:0.005s]
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:06 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    # Update product average rating
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    # Update type-specific fields
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    # Update type-specific fields
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    cart_item = CartItem.query.filter_by(id=cart_item_id, user_id=user.id).first()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    wishlist_item = Wishlist.query.filter_by(id=item_id, user_id=user.id).first()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    wishlist_item = Wishlist.query.filter_by(id=item_id, user_id=user.id).first()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    # Add user interaction data if user is authenticated
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    'product_name': item.product_name,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
    return jsonify({
ValueError: too many values to unpack (expected 2)
2025-07-12 17:28:07 - allora - ERROR - Frontend Error: 
2025-07-12 17:31:48 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:31:48 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    # Update type-specific fields
ValueError: too many values to unpack (expected 2)
2025-07-12 17:32:05 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:32:05 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    # Update type-specific fields
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:27 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.012s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 17:38:27 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:38:27 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:38:27 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.002s]
2025-07-12 17:38:27 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:38:27 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.002s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_products [status:200 duration:0.004s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_products/_stats [status:200 duration:0.003s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_products [status:200 duration:0.004s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_search_analytics [status:200 duration:0.002s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_search_analytics/_stats [status:200 duration:0.005s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_search_analytics [status:200 duration:0.002s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_suggestions [status:200 duration:0.003s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_suggestions/_stats [status:200 duration:0.003s]
2025-07-12 17:38:27 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_suggestions [status:200 duration:0.004s]
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    'created_at': new_order.created_at.isoformat(),
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:28 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    # Check if user already reviewed this product
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    'nickname': payment_method.nickname,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    'nickname': payment_method.nickname,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    cart_item = CartItem.query.filter_by(user_id=user.id, product_id=product_id).first()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def move_wishlist_to_cart(item_id):
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def move_wishlist_to_cart(item_id):
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    # Generate order number
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    # Add user interaction data if user is authenticated
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    order_items = OrderItem.query.filter_by(order_id=invoice.order_id).all()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
ValueError: too many values to unpack (expected 2)
2025-07-12 17:38:29 - allora - ERROR - Frontend Error: 
2025-07-12 17:42:21 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 17:42:21 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 17:42:21 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 17:42:21 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 17:42:21 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 17:42:21 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.002s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_products [status:200 duration:0.002s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_products/_stats [status:200 duration:0.004s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_products [status:200 duration:0.002s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_search_analytics [status:200 duration:0.004s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_search_analytics/_stats [status:200 duration:0.003s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_search_analytics [status:200 duration:0.004s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_suggestions [status:200 duration:0.004s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_suggestions/_stats [status:200 duration:0.004s]
2025-07-12 17:42:21 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_suggestions [status:200 duration:0.003s]
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at addresses
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7016, in addresses
    'id': user.id,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at payment_methods
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7455, in payment_methods
    'created_at': new_order.created_at.isoformat(),
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at recently_viewed
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 8091, in recently_viewed
    # Check if user already reviewed this product
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    'nickname': payment_method.nickname,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at cart
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7614, in cart
    'nickname': payment_method.nickname,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at get_smart_bundles
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7724, in get_smart_bundles
    cart_item = CartItem.query.filter_by(user_id=user.id, product_id=product_id).first()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at orders
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at order_detail
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def move_wishlist_to_cart(item_id):
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7308, in orders
    def move_wishlist_to_cart(item_id):
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7409, in order_detail
    # Generate order number
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at wishlist
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at community_posts
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 7174, in wishlist
    'address_type': address.address_type,
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at get_invoices
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 6573, in community_posts
    # Add user interaction data if user is authenticated
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:23 - allora - ERROR - Unhandled exception: ValueError at refunds
2025-07-12 17:42:24 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11314, in get_invoices
    order_items = OrderItem.query.filter_by(order_id=invoice.order_id).all()
ValueError: too many values to unpack (expected 2)
2025-07-12 17:42:24 - allora - ERROR - Frontend Error: 
2025-07-12 17:42:24 - allora - ERROR - Unhandled exception: too many values to unpack (expected 2)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "C:\Users\<USER>\Desktop\allora_project\allora\backend\app.py", line 11402, in refunds
ValueError: too many values to unpack (expected 2)
2025-07-12 18:29:20 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.006s]
2025-07-12 18:29:20 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.021s]
2025-07-12 18:29:20 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 18:29:22 - allora - WARNING - 404 error for path: /api/register
2025-07-12 18:46:54 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 18:46:54 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 18:46:54 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 18:46:54 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 18:46:54 - startup - INFO - ============================================================
2025-07-12 18:46:54 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 18:46:54 - startup - INFO - ============================================================
2025-07-12 18:46:54 - startup - INFO - 📅 Startup Time: 2025-07-12T18:46:54.716378
2025-07-12 18:46:54 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 18:46:54 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 18:46:54 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 18:46:54 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 18:46:54 - startup - INFO - ============================================================
2025-07-12 18:46:54 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 18:46:54 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.010s]
2025-07-12 18:46:54 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 18:46:54 - allora - INFO - Search API blueprint registered successfully
2025-07-12 18:46:54 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 18:46:55 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 18:46:55 - allora - WARNING - Could not initialize Redis for behavior tracker: COMPARE
2025-07-12 18:46:55 - allora - WARNING - Error with user behavior API: COMPARE
2025-07-12 18:46:55 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 18:46:55 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 18:46:55 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 18:46:55 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 18:46:55 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 18:46:55 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 18:46:55 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 18:46:55 - allora - INFO - Recommendation system initialized successfully
2025-07-12 18:46:55 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 18:46:55 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 18:46:55 - tracking_system - INFO - Real-time tracking system started
2025-07-12 18:46:55 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 18:46:55 - notification_service - INFO - Notification delivery service started
2025-07-12 18:46:55 - allora - INFO - Notification service initialized successfully
2025-07-12 18:46:55 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 18:46:55 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 20:23:59 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 20:23:59 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 20:23:59 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 20:23:59 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 20:23:59 - startup - INFO - ============================================================
2025-07-12 20:23:59 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 20:23:59 - startup - INFO - ============================================================
2025-07-12 20:23:59 - startup - INFO - 📅 Startup Time: 2025-07-12T20:23:59.282425
2025-07-12 20:23:59 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 20:23:59 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 20:23:59 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 20:23:59 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 20:23:59 - startup - INFO - ============================================================
2025-07-12 20:23:59 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 20:23:59 - allora - INFO - Search API blueprint registered successfully
2025-07-12 20:23:59 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 20:23:59 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 20:23:59 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 20:23:59 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 20:23:59 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 20:23:59 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 20:23:59 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 20:23:59 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 20:23:59 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 20:23:59 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 20:23:59 - allora - INFO - Recommendation system initialized successfully
2025-07-12 20:23:59 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 20:23:59 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 20:23:59 - tracking_system - INFO - Real-time tracking system started
2025-07-12 20:23:59 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 20:23:59 - notification_service - INFO - Notification delivery service started
2025-07-12 20:23:59 - allora - INFO - Notification service initialized successfully
2025-07-12 20:23:59 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 20:23:59 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 20:24:00 - elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 20:24:00 - elasticsearch_config - INFO - Elasticsearch is disabled - running without search functionality
2025-07-12 20:31:34 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 20:31:34 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 20:31:34 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 20:31:34 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 20:31:34 - startup - INFO - ============================================================
2025-07-12 20:31:34 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 20:31:34 - startup - INFO - ============================================================
2025-07-12 20:31:34 - startup - INFO - 📅 Startup Time: 2025-07-12T20:31:34.059794
2025-07-12 20:31:34 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 20:31:34 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 20:31:34 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 20:31:34 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 20:31:34 - startup - INFO - ============================================================
2025-07-12 20:31:34 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 20:31:34 - allora - INFO - Search API blueprint registered successfully
2025-07-12 20:31:34 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 20:31:34 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 20:31:34 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 20:31:35 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 20:31:35 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 20:31:35 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 20:31:35 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 20:31:35 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 20:31:35 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 20:31:35 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 20:31:35 - allora - INFO - Recommendation system initialized successfully
2025-07-12 20:31:35 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 20:31:35 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 20:31:35 - tracking_system - INFO - Real-time tracking system started
2025-07-12 20:31:35 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 20:31:35 - notification_service - INFO - Notification delivery service started
2025-07-12 20:31:35 - allora - INFO - Notification service initialized successfully
2025-07-12 20:31:35 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 20:31:35 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 20:31:35 - elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 20:31:35 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.005s]
2025-07-12 20:31:35 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 20:31:35 - elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 20:31:35 - elasticsearch_config - INFO - Elasticsearch is disabled - running without search functionality
2025-07-12 20:32:15 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 20:32:15 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 20:32:15 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 20:32:15 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 20:32:15 - startup - INFO - ============================================================
2025-07-12 20:32:15 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 20:32:15 - startup - INFO - ============================================================
2025-07-12 20:32:15 - startup - INFO - 📅 Startup Time: 2025-07-12T20:32:15.269865
2025-07-12 20:32:15 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 20:32:15 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 20:32:15 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 20:32:15 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 20:32:15 - startup - INFO - ============================================================
2025-07-12 20:32:15 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 20:32:15 - allora - INFO - Search API blueprint registered successfully
2025-07-12 20:32:15 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 20:32:15 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 20:32:15 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 20:32:15 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 20:32:15 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 20:32:15 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 20:32:15 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 20:32:15 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 20:32:15 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 20:32:15 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 20:32:15 - allora - INFO - Recommendation system initialized successfully
2025-07-12 20:32:15 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 20:32:15 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 20:32:15 - tracking_system - INFO - Real-time tracking system started
2025-07-12 20:32:15 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 20:32:15 - notification_service - INFO - Notification delivery service started
2025-07-12 20:32:15 - allora - INFO - Notification service initialized successfully
2025-07-12 20:32:15 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 20:32:15 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 20:32:16 - elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 20:32:16 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.010s]
2025-07-12 20:32:16 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 20:32:16 - elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 20:32:16 - elasticsearch_config - INFO - Elasticsearch is disabled - running without search functionality
2025-07-12 21:30:19 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 21:30:19 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 21:30:19 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 21:30:19 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 21:30:19 - startup - INFO - ============================================================
2025-07-12 21:30:19 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 21:30:19 - startup - INFO - ============================================================
2025-07-12 21:30:19 - startup - INFO - 📅 Startup Time: 2025-07-12T21:30:19.361997
2025-07-12 21:30:19 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 21:30:19 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-12 21:30:19 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 21:30:19 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 21:30:19 - startup - INFO - ============================================================
2025-07-12 21:30:19 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 21:30:19 - allora - INFO - Search API blueprint registered successfully
2025-07-12 21:30:19 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 21:30:19 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 21:30:19 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 21:30:19 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 21:30:19 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 21:30:19 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 21:30:19 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 21:30:19 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 21:30:19 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 21:30:19 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 21:30:19 - allora - INFO - Recommendation system initialized successfully
2025-07-12 21:30:19 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 21:30:19 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 21:30:19 - tracking_system - INFO - Real-time tracking system started
2025-07-12 21:30:19 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 21:30:19 - notification_service - INFO - Notification delivery service started
2025-07-12 21:30:19 - allora - INFO - Notification service initialized successfully
2025-07-12 21:30:19 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 21:30:19 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 21:30:20 - search_system.elasticsearch_search - ERROR - Database fallback search error: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-07-12 21:33:16 - allora - WARNING - 404 error for path: /favicon.ico
2025-07-12 22:02:22 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.010s]
2025-07-12 22:02:22 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.006s]
2025-07-12 22:02:22 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:28 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 22:02:28 - elasticsearch_search - ERROR - Elasticsearch search error: BadRequestError(400, 'search_phase_execution_exception', 'No mapping found for [id] in order to sort on')
2025-07-12 22:02:28 - elasticsearch_search - ERROR - Database fallback search error: 'Product' object has no attribute 'image_url'
2025-07-12 22:02:32 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.007s]
2025-07-12 22:02:32 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 22:02:32 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:32 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.005s]
2025-07-12 22:02:33 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:02:33 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:33 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.008s]
2025-07-12 22:02:33 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.002s]
2025-07-12 22:02:33 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:33 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 22:02:33 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.002s]
2025-07-12 22:02:33 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:34 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.006s]
2025-07-12 22:02:34 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:02:34 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:35 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.007s]
2025-07-12 22:02:35 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:02:35 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:39 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.040s]
2025-07-12 22:02:39 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:02:39 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:42 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 22:02:42 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:02:42 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:43 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.003s]
2025-07-12 22:02:43 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:02:43 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:47 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.005s]
2025-07-12 22:02:47 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.002s]
2025-07-12 22:02:47 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:47 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.005s]
2025-07-12 22:02:47 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:02:47 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:48 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.007s]
2025-07-12 22:02:48 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 22:02:48 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:48 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 22:02:48 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 22:02:48 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:48 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 22:02:48 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:02:48 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:48 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.003s]
2025-07-12 22:02:48 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 22:02:48 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:49 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.010s]
2025-07-12 22:02:49 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.005s]
2025-07-12 22:02:49 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:49 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.006s]
2025-07-12 22:02:49 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:02:49 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:49 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 22:02:49 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 22:02:49 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:49 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.006s]
2025-07-12 22:02:49 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.004s]
2025-07-12 22:02:49 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:49 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 22:02:49 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.008s]
2025-07-12 22:02:49 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:02:50 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 22:02:50 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.005s]
2025-07-12 22:02:50 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:03:00 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.003s]
2025-07-12 22:03:00 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:03:00 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:03:01 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 22:03:01 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:03:01 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:03:03 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.003s]
2025-07-12 22:03:03 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.007s]
2025-07-12 22:03:03 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:03:04 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.003s]
2025-07-12 22:03:04 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:03:04 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:03:07 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.002s]
2025-07-12 22:03:07 - elasticsearch_search - ERROR - Similar products error: BadRequestError(400, 'parsing_exception', 'Unknown key for a START_OBJECT in [filter].')
2025-07-12 22:03:14 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.007s]
2025-07-12 22:03:15 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.003s]
2025-07-12 22:03:15 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.003s]
2025-07-12 22:03:28 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 22:03:28 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:03:28 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
2025-07-12 22:04:00 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.002s]
2025-07-12 22:04:00 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_products [status:200 duration:0.002s]
2025-07-12 22:04:00 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_products/_stats [status:200 duration:0.004s]
2025-07-12 22:04:00 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_products [status:200 duration:0.003s]
2025-07-12 22:04:00 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_search_analytics [status:200 duration:0.003s]
2025-07-12 22:04:00 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_search_analytics/_stats [status:200 duration:0.002s]
2025-07-12 22:04:00 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_search_analytics [status:200 duration:0.002s]
2025-07-12 22:04:00 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_suggestions [status:200 duration:0.003s]
2025-07-12 22:04:00 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_suggestions/_stats [status:200 duration:0.002s]
2025-07-12 22:04:00 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_suggestions [status:200 duration:0.003s]
2025-07-12 22:04:05 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.004s]
2025-07-12 22:04:16 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.002s]
2025-07-12 22:04:16 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_products [status:200 duration:0.003s]
2025-07-12 22:04:16 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_products/_stats [status:200 duration:0.002s]
2025-07-12 22:04:16 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_products [status:200 duration:0.003s]
2025-07-12 22:04:16 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_search_analytics [status:200 duration:0.002s]
2025-07-12 22:04:16 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_search_analytics/_stats [status:200 duration:0.004s]
2025-07-12 22:04:16 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_search_analytics [status:200 duration:0.003s]
2025-07-12 22:04:16 - elastic_transport.transport - INFO - HEAD http://localhost:9200/allora_suggestions [status:200 duration:0.002s]
2025-07-12 22:04:16 - elastic_transport.transport - INFO - GET http://localhost:9200/allora_suggestions/_stats [status:200 duration:0.002s]
2025-07-12 22:04:16 - elastic_transport.transport - INFO - GET http://localhost:9200/_cluster/health/allora_suggestions [status:200 duration:0.002s]
2025-07-12 22:04:28 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.003s]
2025-07-12 22:04:28 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:400 duration:0.003s]
2025-07-12 22:04:28 - elasticsearch_search - ERROR - Spell suggestion error: BadRequestError(400, 'search_phase_execution_exception', 'no mapping found for field [name]', no mapping found for field [name])
