2025-07-13 10:16:09 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:16:09 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:16:09 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:16:09 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:16:09 - startup - INFO - ============================================================
2025-07-13 10:16:09 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:16:09 - startup - INFO - ============================================================
2025-07-13 10:16:09 - startup - INFO - 📅 Startup Time: 2025-07-13T10:16:09.588100
2025-07-13 10:16:09 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:16:09 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:16:09 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:16:09 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:16:09 - startup - INFO - ============================================================
2025-07-13 10:16:09 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:16:09 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.009s]
2025-07-13 10:16:09 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:16:10 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:16:10 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:16:10 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:16:10 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:16:10 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:16:10 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:16:10 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:16:10 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:16:10 - notification_service - INFO - Notification delivery service started
2025-07-13 10:16:10 - allora - INFO - Notification service initialized successfully
2025-07-13 10:16:10 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:20:42 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:20:42 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:20:42 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:20:42 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:20:42 - startup - INFO - ============================================================
2025-07-13 10:20:42 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:20:42 - startup - INFO - ============================================================
2025-07-13 10:20:42 - startup - INFO - 📅 Startup Time: 2025-07-13T10:20:42.293987
2025-07-13 10:20:42 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:20:42 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:20:42 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:20:42 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:20:42 - startup - INFO - ============================================================
2025-07-13 10:20:42 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:20:42 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 10:20:42 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:20:42 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:20:42 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:20:43 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:20:43 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:20:43 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:20:43 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:20:43 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:20:43 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:20:43 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:20:43 - notification_service - INFO - Notification delivery service started
2025-07-13 10:20:43 - allora - INFO - Notification service initialized successfully
2025-07-13 10:20:43 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:30:26 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:30:26 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:30:26 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:30:26 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:30:26 - startup - INFO - ============================================================
2025-07-13 10:30:26 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:30:26 - startup - INFO - ============================================================
2025-07-13 10:30:26 - startup - INFO - 📅 Startup Time: 2025-07-13T10:30:26.730480
2025-07-13 10:30:26 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:30:26 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:30:26 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:30:26 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:30:26 - startup - INFO - ============================================================
2025-07-13 10:30:26 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:30:27 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 10:30:27 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:30:27 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:30:27 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:30:27 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:30:27 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:30:27 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:30:27 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:30:27 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:30:27 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:30:27 - notification_service - INFO - Notification delivery service started
2025-07-13 10:30:27 - allora - INFO - Notification service initialized successfully
2025-07-13 10:30:27 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:33:40 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:33:40 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:33:40 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:33:40 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:33:40 - startup - INFO - ============================================================
2025-07-13 10:33:40 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:33:40 - startup - INFO - ============================================================
2025-07-13 10:33:40 - startup - INFO - 📅 Startup Time: 2025-07-13T10:33:40.762640
2025-07-13 10:33:40 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:33:40 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:33:40 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:33:40 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:33:40 - startup - INFO - ============================================================
2025-07-13 10:33:40 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:33:41 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 10:33:41 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:33:41 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:33:41 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:33:41 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:33:41 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:33:41 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:33:41 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:33:41 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:33:41 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:33:41 - notification_service - INFO - Notification delivery service started
2025-07-13 10:33:41 - allora - INFO - Notification service initialized successfully
2025-07-13 10:33:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:34:12 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:34:12 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:34:12 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:34:12 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:34:12 - startup - INFO - ============================================================
2025-07-13 10:34:12 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:34:12 - startup - INFO - ============================================================
2025-07-13 10:34:12 - startup - INFO - 📅 Startup Time: 2025-07-13T10:34:12.516263
2025-07-13 10:34:12 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:34:12 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:34:12 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:34:12 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:34:12 - startup - INFO - ============================================================
2025-07-13 10:34:12 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:34:12 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 10:34:12 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:34:13 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:34:13 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:34:13 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:34:13 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:34:13 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:34:13 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:34:13 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:34:13 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:34:13 - notification_service - INFO - Notification delivery service started
2025-07-13 10:34:13 - allora - INFO - Notification service initialized successfully
2025-07-13 10:34:13 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:37:08 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:37:08 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:37:08 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:37:08 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:37:08 - startup - INFO - ============================================================
2025-07-13 10:37:08 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:37:08 - startup - INFO - ============================================================
2025-07-13 10:37:08 - startup - INFO - 📅 Startup Time: 2025-07-13T10:37:08.658094
2025-07-13 10:37:08 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:37:08 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:37:08 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:37:08 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:37:08 - startup - INFO - ============================================================
2025-07-13 10:37:08 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:37:09 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 10:37:09 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:37:09 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:37:09 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:37:09 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:37:09 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:37:09 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:37:09 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:37:09 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:37:09 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:37:09 - notification_service - INFO - Notification delivery service started
2025-07-13 10:37:09 - allora - INFO - Notification service initialized successfully
2025-07-13 10:37:09 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:37:45 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:37:45 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:37:45 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:37:45 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:37:45 - startup - INFO - ============================================================
2025-07-13 10:37:45 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:37:45 - startup - INFO - ============================================================
2025-07-13 10:37:45 - startup - INFO - 📅 Startup Time: 2025-07-13T10:37:45.483857
2025-07-13 10:37:45 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:37:45 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:37:45 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:37:45 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:37:45 - startup - INFO - ============================================================
2025-07-13 10:37:45 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:37:45 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 10:37:45 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:37:45 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:37:45 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:37:46 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:37:46 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:37:46 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:37:46 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:37:46 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:37:46 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:37:46 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:37:46 - notification_service - INFO - Notification delivery service started
2025-07-13 10:37:46 - allora - INFO - Notification service initialized successfully
2025-07-13 10:37:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:41:45 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:41:45 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:41:45 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:41:45 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:41:45 - startup - INFO - ============================================================
2025-07-13 10:41:45 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:41:45 - startup - INFO - ============================================================
2025-07-13 10:41:45 - startup - INFO - 📅 Startup Time: 2025-07-13T10:41:45.507218
2025-07-13 10:41:45 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:41:45 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:41:45 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:41:45 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:41:45 - startup - INFO - ============================================================
2025-07-13 10:41:45 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:41:45 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 10:41:45 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:41:45 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:41:45 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:41:46 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:41:46 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:41:46 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:41:46 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:41:46 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:41:46 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:41:46 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:41:46 - notification_service - INFO - Notification delivery service started
2025-07-13 10:41:46 - allora - INFO - Notification service initialized successfully
2025-07-13 10:41:46 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:49:56 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:49:56 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:49:56 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:49:56 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:49:56 - startup - INFO - ============================================================
2025-07-13 10:49:56 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:49:56 - startup - INFO - ============================================================
2025-07-13 10:49:56 - startup - INFO - 📅 Startup Time: 2025-07-13T10:49:56.837879
2025-07-13 10:49:56 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:49:56 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:49:56 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:49:56 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:49:56 - startup - INFO - ============================================================
2025-07-13 10:49:56 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:49:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 10:49:57 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:49:57 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:49:57 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:49:57 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:49:57 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:49:57 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:49:57 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:49:57 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:49:57 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:49:57 - notification_service - INFO - Notification delivery service started
2025-07-13 10:49:57 - allora - INFO - Notification service initialized successfully
2025-07-13 10:49:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 10:56:33 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 10:56:33 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 10:56:33 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 10:56:33 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 10:56:33 - startup - INFO - ============================================================
2025-07-13 10:56:33 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 10:56:33 - startup - INFO - ============================================================
2025-07-13 10:56:33 - startup - INFO - 📅 Startup Time: 2025-07-13T10:56:33.586153
2025-07-13 10:56:33 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 10:56:33 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 10:56:33 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 10:56:33 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 10:56:33 - startup - INFO - ============================================================
2025-07-13 10:56:33 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 10:56:33 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 10:56:33 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 10:56:33 - allora - INFO - Search API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 10:56:34 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 10:56:34 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 10:56:34 - allora - INFO - Recommendation system initialized successfully
2025-07-13 10:56:34 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 10:56:34 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 10:56:34 - tracking_system - INFO - Real-time tracking system started
2025-07-13 10:56:34 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 10:56:34 - notification_service - INFO - Notification delivery service started
2025-07-13 10:56:34 - allora - INFO - Notification service initialized successfully
2025-07-13 10:56:34 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:00:30 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:00:30 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:00:30 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:00:30 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:00:30 - startup - INFO - ============================================================
2025-07-13 11:00:30 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:00:30 - startup - INFO - ============================================================
2025-07-13 11:00:30 - startup - INFO - 📅 Startup Time: 2025-07-13T11:00:30.757210
2025-07-13 11:00:30 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:00:30 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:00:30 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:00:30 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:00:30 - startup - INFO - ============================================================
2025-07-13 11:00:30 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:00:31 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 11:00:31 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:00:31 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:00:31 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:00:31 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:00:31 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:00:31 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:00:31 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:00:31 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:00:31 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:00:31 - notification_service - INFO - Notification delivery service started
2025-07-13 11:00:31 - allora - INFO - Notification service initialized successfully
2025-07-13 11:00:31 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:03:49 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:03:49 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:03:49 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:03:49 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:03:49 - startup - INFO - ============================================================
2025-07-13 11:03:49 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:03:49 - startup - INFO - ============================================================
2025-07-13 11:03:49 - startup - INFO - 📅 Startup Time: 2025-07-13T11:03:49.657566
2025-07-13 11:03:49 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:03:49 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:03:49 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:03:49 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:03:49 - startup - INFO - ============================================================
2025-07-13 11:03:49 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:03:50 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:03:50 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:03:50 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:03:50 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:03:50 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:03:50 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:03:50 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:03:50 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:03:50 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:03:50 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:03:50 - notification_service - INFO - Notification delivery service started
2025-07-13 11:03:50 - allora - INFO - Notification service initialized successfully
2025-07-13 11:03:50 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:07:17 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:07:17 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:07:17 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:07:17 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:07:17 - startup - INFO - ============================================================
2025-07-13 11:07:17 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:07:17 - startup - INFO - ============================================================
2025-07-13 11:07:17 - startup - INFO - 📅 Startup Time: 2025-07-13T11:07:17.025802
2025-07-13 11:07:17 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:07:17 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:07:17 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:07:17 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:07:17 - startup - INFO - ============================================================
2025-07-13 11:07:17 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:07:17 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 11:07:17 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:07:17 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:07:17 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:07:17 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:07:17 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:07:17 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:07:17 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:07:17 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:07:17 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:07:17 - notification_service - INFO - Notification delivery service started
2025-07-13 11:07:17 - allora - INFO - Notification service initialized successfully
2025-07-13 11:07:17 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:10:15 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:10:15 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:10:15 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:10:15 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:10:15 - startup - INFO - ============================================================
2025-07-13 11:10:15 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:10:15 - startup - INFO - ============================================================
2025-07-13 11:10:15 - startup - INFO - 📅 Startup Time: 2025-07-13T11:10:15.497785
2025-07-13 11:10:15 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:10:15 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:10:15 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:10:15 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:10:15 - startup - INFO - ============================================================
2025-07-13 11:10:15 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:10:15 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.005s]
2025-07-13 11:10:15 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:10:15 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:10:15 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:10:16 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:10:16 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:10:16 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:10:16 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:10:16 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:10:16 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:10:16 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:10:16 - notification_service - INFO - Notification delivery service started
2025-07-13 11:10:16 - allora - INFO - Notification service initialized successfully
2025-07-13 11:10:16 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:21:02 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:21:02 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:21:02 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:21:02 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:21:02 - startup - INFO - ============================================================
2025-07-13 11:21:02 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:21:02 - startup - INFO - ============================================================
2025-07-13 11:21:02 - startup - INFO - 📅 Startup Time: 2025-07-13T11:21:02.250008
2025-07-13 11:21:02 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:21:02 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:21:02 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:21:02 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:21:02 - startup - INFO - ============================================================
2025-07-13 11:21:02 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:21:02 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 11:21:02 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:21:02 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:21:02 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:21:04 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:21:04 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:21:04 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:21:04 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:21:04 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:21:04 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:21:04 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:21:04 - notification_service - INFO - Notification delivery service started
2025-07-13 11:21:04 - allora - INFO - Notification service initialized successfully
2025-07-13 11:21:04 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:21:40 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:21:40 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:21:40 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:21:40 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:21:40 - startup - INFO - ============================================================
2025-07-13 11:21:40 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:21:40 - startup - INFO - ============================================================
2025-07-13 11:21:40 - startup - INFO - 📅 Startup Time: 2025-07-13T11:21:40.317507
2025-07-13 11:21:40 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:21:40 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:21:40 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:21:40 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:21:40 - startup - INFO - ============================================================
2025-07-13 11:21:40 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:21:40 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:21:40 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:21:40 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:21:40 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:21:41 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:21:41 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:21:41 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:21:41 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:21:41 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:21:41 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:21:41 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:21:41 - notification_service - INFO - Notification delivery service started
2025-07-13 11:21:41 - allora - INFO - Notification service initialized successfully
2025-07-13 11:21:41 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:28:32 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:28:32 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:28:32 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:28:32 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:28:32 - startup - INFO - ============================================================
2025-07-13 11:28:32 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:28:32 - startup - INFO - ============================================================
2025-07-13 11:28:32 - startup - INFO - 📅 Startup Time: 2025-07-13T11:28:32.825546
2025-07-13 11:28:32 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:28:32 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:28:32 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:28:32 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:28:32 - startup - INFO - ============================================================
2025-07-13 11:28:32 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:28:33 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-13 11:28:33 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:28:33 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:28:33 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:28:33 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:28:33 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:28:33 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:28:33 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:28:33 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:28:33 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:28:33 - notification_service - INFO - Notification delivery service started
2025-07-13 11:28:33 - allora - INFO - Notification service initialized successfully
2025-07-13 11:28:33 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:29:31 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:29:31 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:29:31 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:29:31 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:29:31 - startup - INFO - ============================================================
2025-07-13 11:29:31 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:29:31 - startup - INFO - ============================================================
2025-07-13 11:29:31 - startup - INFO - 📅 Startup Time: 2025-07-13T11:29:31.287888
2025-07-13 11:29:31 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:29:31 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:29:31 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:29:31 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:29:31 - startup - INFO - ============================================================
2025-07-13 11:29:31 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:29:31 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.007s]
2025-07-13 11:29:31 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:29:31 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:29:31 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:29:31 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:29:31 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:29:32 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:29:32 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:29:32 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:29:32 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:29:32 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:29:32 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:29:32 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:29:32 - notification_service - INFO - Notification delivery service started
2025-07-13 11:29:32 - allora - INFO - Notification service initialized successfully
2025-07-13 11:29:32 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
2025-07-13 11:31:34 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-13 11:31:34 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-13 11:31:34 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-13 11:31:34 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-13 11:31:34 - startup - INFO - ============================================================
2025-07-13 11:31:34 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-13 11:31:34 - startup - INFO - ============================================================
2025-07-13 11:31:34 - startup - INFO - 📅 Startup Time: 2025-07-13T11:31:34.977800
2025-07-13 11:31:34 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-13 11:31:34 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project\allora\backend
2025-07-13 11:31:34 - startup - INFO - 🔧 Log Level: INFO
2025-07-13 11:31:34 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-13 11:31:34 - startup - INFO - ============================================================
2025-07-13 11:31:34 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-13 11:31:35 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.008s]
2025-07-13 11:31:35 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-13 11:31:35 - allora - INFO - Search API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Behavior tracker initialized successfully
2025-07-13 11:31:35 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - RMA API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-13 11:31:35 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-13 11:31:35 - allora - INFO - Recommendation system initialized successfully
2025-07-13 11:31:35 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-13 11:31:35 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-13 11:31:35 - tracking_system - INFO - Real-time tracking system started
2025-07-13 11:31:35 - allora - INFO - Real-time tracking system initialized successfully
2025-07-13 11:31:35 - notification_service - INFO - Notification delivery service started
2025-07-13 11:31:35 - allora - INFO - Notification service initialized successfully
2025-07-13 11:31:35 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\RecommendationModel\\recommendation_model.pkl'
