"""
Advanced Search Query Builder System
Provides flexible query building for complex Elasticsearch searches
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timezone
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Supported query types"""
    MATCH = "match"
    MULTI_MATCH = "multi_match"
    TERM = "term"
    TERMS = "terms"
    RANGE = "range"
    BOOL = "bool"
    FUZZY = "fuzzy"
    WILDCARD = "wildcard"
    REGEXP = "regexp"
    PREFIX = "prefix"
    EXISTS = "exists"
    NESTED = "nested"
    FUNCTION_SCORE = "function_score"

class BoolOperator(Enum):
    """Boolean operators for combining queries"""
    MUST = "must"
    SHOULD = "should"
    MUST_NOT = "must_not"
    FILTER = "filter"

class SortOrder(Enum):
    """Sort order options"""
    ASC = "asc"
    DESC = "desc"

@dataclass
class SearchField:
    """Represents a searchable field with boost and analyzer settings"""
    name: str
    boost: float = 1.0
    analyzer: Optional[str] = None
    fuzziness: Optional[Union[str, int]] = None

@dataclass
class RangeFilter:
    """Represents a range filter for numeric or date fields"""
    field: str
    gte: Optional[Union[int, float, str]] = None  # Greater than or equal
    lte: Optional[Union[int, float, str]] = None  # Less than or equal
    gt: Optional[Union[int, float, str]] = None   # Greater than
    lt: Optional[Union[int, float, str]] = None   # Less than
    format: Optional[str] = None  # For date fields

@dataclass
class SortField:
    """Represents a sort field with order and options"""
    field: str
    order: SortOrder = SortOrder.ASC
    missing: Optional[str] = None  # "_last" or "_first"
    mode: Optional[str] = None     # "min", "max", "sum", "avg", "median"

@dataclass
class FunctionScore:
    """Represents a function score configuration"""
    function_type: str  # "field_value_factor", "script_score", "random_score", etc.
    field: Optional[str] = None
    factor: Optional[float] = None
    modifier: Optional[str] = None  # "none", "log", "log1p", "log2p", "ln", "ln1p", "ln2p", "square", "sqrt", "reciprocal"
    missing: Optional[float] = None
    script: Optional[Dict[str, Any]] = None
    weight: Optional[float] = None

class SearchQueryBuilder:
    """Advanced search query builder for Elasticsearch"""
    
    def __init__(self):
        self.query_body = {
            "query": {},
            "sort": [],
            "aggs": {},
            "highlight": {},
            "size": 20,
            "from": 0
        }
        self.bool_query = {
            "must": [],
            "should": [],
            "must_not": [],
            "filter": []
        }
        self.function_scores = []
        self.aggregations = {}
        self.highlights = {}
    
    def reset(self) -> 'SearchQueryBuilder':
        """Reset the query builder to initial state"""
        self.__init__()
        return self

    def add_match_query(
        self,
        field: str,
        query: str,
        operator: BoolOperator = BoolOperator.MUST,
        boost: float = 1.0,
        fuzziness: Optional[Union[str, int]] = None,
        analyzer: Optional[str] = None
    ) -> 'SearchQueryBuilder':
        """Add a match query for a specific field"""
        match_query = {
            "match": {
                field: {
                    "query": query,
                    "boost": boost
                }
            }
        }

        # Add optional parameters
        if fuzziness is not None:
            match_query["match"][field]["fuzziness"] = fuzziness

        if analyzer is not None:
            match_query["match"][field]["analyzer"] = analyzer

        # Add to appropriate bool clause
        self.bool_query[operator.value].append(match_query)
        return self

    def add_term_filter(
        self,
        field: str,
        value: Union[str, int, float, bool],
        operator: BoolOperator = BoolOperator.FILTER,
        boost: float = 1.0
    ) -> 'SearchQueryBuilder':
        """Add a term filter for exact matching"""
        term_query = {
            "term": {
                field: {
                    "value": value,
                    "boost": boost
                }
            }
        }

        # Add to appropriate bool clause
        self.bool_query[operator.value].append(term_query)
        return self
    
    def add_text_search(
        self,
        query: str,
        fields: List[SearchField],
        operator: str = "and",
        minimum_should_match: Optional[str] = None,
        bool_operator: BoolOperator = BoolOperator.MUST
    ) -> 'SearchQueryBuilder':
        """Add text search query"""
        if not query.strip():
            return self
        
        # Build field list with boosts
        field_list = []
        for field in fields:
            field_str = field.name
            if field.boost != 1.0:
                field_str += f"^{field.boost}"
            field_list.append(field_str)
        
        multi_match_query = {
            "multi_match": {
                "query": query.strip(),
                "fields": field_list,
                "type": "best_fields",
                "operator": operator
            }
        }
        
        if minimum_should_match:
            multi_match_query["multi_match"]["minimum_should_match"] = minimum_should_match
        
        # Add fuzziness if specified
        if any(field.fuzziness for field in fields):
            multi_match_query["multi_match"]["fuzziness"] = "AUTO"
        
        self.bool_query[bool_operator.value].append(multi_match_query)
        return self
    
    def add_exact_match(
        self,
        field: str,
        value: Union[str, int, float, bool],
        bool_operator: BoolOperator = BoolOperator.FILTER
    ) -> 'SearchQueryBuilder':
        """Add exact match query"""
        term_query = {
            "term": {
                field: value
            }
        }
        self.bool_query[bool_operator.value].append(term_query)
        return self
    
    def add_terms_match(
        self,
        field: str,
        values: List[Union[str, int, float]],
        bool_operator: BoolOperator = BoolOperator.FILTER
    ) -> 'SearchQueryBuilder':
        """Add terms match query for multiple values"""
        if not values:
            return self
        
        terms_query = {
            "terms": {
                field: values
            }
        }
        self.bool_query[bool_operator.value].append(terms_query)
        return self
    
    def add_range_filter(
        self,
        range_filter: RangeFilter,
        bool_operator: BoolOperator = BoolOperator.FILTER
    ) -> 'SearchQueryBuilder':
        """Add range filter"""
        range_query = {"range": {range_filter.field: {}}}
        
        if range_filter.gte is not None:
            range_query["range"][range_filter.field]["gte"] = range_filter.gte
        if range_filter.lte is not None:
            range_query["range"][range_filter.field]["lte"] = range_filter.lte
        if range_filter.gt is not None:
            range_query["range"][range_filter.field]["gt"] = range_filter.gt
        if range_filter.lt is not None:
            range_query["range"][range_filter.field]["lt"] = range_filter.lt
        if range_filter.format:
            range_query["range"][range_filter.field]["format"] = range_filter.format
        
        self.bool_query[bool_operator.value].append(range_query)
        return self
    
    def add_fuzzy_search(
        self,
        field: str,
        value: str,
        fuzziness: Union[str, int] = "AUTO",
        bool_operator: BoolOperator = BoolOperator.SHOULD
    ) -> 'SearchQueryBuilder':
        """Add fuzzy search query"""
        fuzzy_query = {
            "fuzzy": {
                field: {
                    "value": value,
                    "fuzziness": fuzziness
                }
            }
        }
        self.bool_query[bool_operator.value].append(fuzzy_query)
        return self
    
    def add_wildcard_search(
        self,
        field: str,
        pattern: str,
        bool_operator: BoolOperator = BoolOperator.SHOULD
    ) -> 'SearchQueryBuilder':
        """Add wildcard search query"""
        wildcard_query = {
            "wildcard": {
                field: pattern
            }
        }
        self.bool_query[bool_operator.value].append(wildcard_query)
        return self
    
    def add_prefix_search(
        self,
        field: str,
        prefix: str,
        bool_operator: BoolOperator = BoolOperator.SHOULD
    ) -> 'SearchQueryBuilder':
        """Add prefix search query"""
        prefix_query = {
            "prefix": {
                field: prefix
            }
        }
        self.bool_query[bool_operator.value].append(prefix_query)
        return self
    
    def add_exists_filter(
        self,
        field: str,
        bool_operator: BoolOperator = BoolOperator.FILTER
    ) -> 'SearchQueryBuilder':
        """Add exists filter to check if field has a value"""
        exists_query = {
            "exists": {
                "field": field
            }
        }
        self.bool_query[bool_operator.value].append(exists_query)
        return self
    
    def add_function_score(
        self,
        function_score: FunctionScore,
        boost_mode: str = "multiply",
        score_mode: str = "multiply"
    ) -> 'SearchQueryBuilder':
        """Add function score for custom scoring"""
        self.function_scores.append({
            "function_score": function_score,
            "boost_mode": boost_mode,
            "score_mode": score_mode
        })
        return self
    
    def add_sort(
        self,
        sort_field: SortField
    ) -> 'SearchQueryBuilder':
        """Add sort field"""
        sort_config = {
            sort_field.field: {
                "order": sort_field.order.value
            }
        }
        
        if sort_field.missing:
            sort_config[sort_field.field]["missing"] = sort_field.missing
        if sort_field.mode:
            sort_config[sort_field.field]["mode"] = sort_field.mode
        
        self.query_body["sort"].append(sort_config)
        return self
    
    def add_aggregation(
        self,
        name: str,
        agg_config: Dict[str, Any]
    ) -> 'SearchQueryBuilder':
        """Add aggregation"""
        self.aggregations[name] = agg_config
        return self
    
    def add_highlight(
        self,
        fields: List[str],
        fragment_size: int = 150,
        number_of_fragments: int = 3,
        pre_tags: List[str] = None,
        post_tags: List[str] = None
    ) -> 'SearchQueryBuilder':
        """Add highlighting configuration"""
        if pre_tags is None:
            pre_tags = ["<mark>"]
        if post_tags is None:
            post_tags = ["</mark>"]
        
        highlight_config = {
            "fragment_size": fragment_size,
            "number_of_fragments": number_of_fragments,
            "pre_tags": pre_tags,
            "post_tags": post_tags,
            "fields": {}
        }
        
        for field in fields:
            highlight_config["fields"][field] = {}
        
        self.highlights = highlight_config
        return self
    
    def set_pagination(
        self,
        page: int = 1,
        per_page: int = 20
    ) -> 'SearchQueryBuilder':
        """Set pagination parameters"""
        self.query_body["size"] = per_page
        self.query_body["from"] = (page - 1) * per_page
        return self
    
    def build(self) -> Dict[str, Any]:
        """Build the final Elasticsearch query"""
        # Build the main query
        if any(self.bool_query.values()):
            main_query = {"bool": {k: v for k, v in self.bool_query.items() if v}}
        else:
            main_query = {"match_all": {}}
        
        # Apply function scoring if configured
        if self.function_scores:
            function_score_query = {
                "function_score": {
                    "query": main_query,
                    "functions": []
                }
            }
            
            for fs_config in self.function_scores:
                fs = fs_config["function_score"]
                function_def = {}
                
                if fs.function_type == "field_value_factor":
                    function_def["field_value_factor"] = {
                        "field": fs.field,
                        "factor": fs.factor or 1.0,
                        "modifier": fs.modifier or "none"
                    }
                    if fs.missing is not None:
                        function_def["field_value_factor"]["missing"] = fs.missing
                
                elif fs.function_type == "script_score":
                    function_def["script_score"] = fs.script
                
                elif fs.function_type == "random_score":
                    function_def["random_score"] = {}
                
                if fs.weight:
                    function_def["weight"] = fs.weight
                
                function_score_query["function_score"]["functions"].append(function_def)
            
            # Set boost and score modes
            if self.function_scores:
                function_score_query["function_score"]["boost_mode"] = self.function_scores[0]["boost_mode"]
                function_score_query["function_score"]["score_mode"] = self.function_scores[0]["score_mode"]
            
            self.query_body["query"] = function_score_query
        else:
            self.query_body["query"] = main_query
        
        # Add aggregations
        if self.aggregations:
            self.query_body["aggs"] = self.aggregations
        
        # Add highlighting
        if self.highlights:
            self.query_body["highlight"] = self.highlights
        
        return self.query_body
    
    def build_count_query(self) -> Dict[str, Any]:
        """Build a count-only query (no results, just count)"""
        query = self.build()
        return {
            "query": query["query"],
            "size": 0
        }

# Predefined query builders for common search patterns
class ProductSearchQueryBuilder(SearchQueryBuilder):
    """Specialized query builder for product searches"""
    
    def __init__(self):
        super().__init__()
        # Default product search fields with boosts
        self.product_fields = [
            SearchField("name", boost=3.0),
            SearchField("category", boost=2.0),
            SearchField("brand", boost=2.0),
            SearchField("description", boost=1.0),
            SearchField("search_keywords", boost=2.5),
            SearchField("material", boost=1.5)
        ]
    
    def add_product_text_search(
        self,
        query: str,
        minimum_should_match: str = "75%"
    ) -> 'ProductSearchQueryBuilder':
        """Add product-specific text search"""
        return self.add_text_search(
            query=query,
            fields=self.product_fields,
            operator="and",
            minimum_should_match=minimum_should_match,
            bool_operator=BoolOperator.MUST
        )
    
    def add_category_filter(
        self,
        categories: List[str]
    ) -> 'ProductSearchQueryBuilder':
        """Add category filter"""
        if categories:
            return self.add_terms_match("category", categories)
        return self
    
    def add_brand_filter(
        self,
        brands: List[str]
    ) -> 'ProductSearchQueryBuilder':
        """Add brand filter"""
        if brands:
            return self.add_terms_match("brand", brands)
        return self
    
    def add_price_range(
        self,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None
    ) -> 'ProductSearchQueryBuilder':
        """Add price range filter"""
        if min_price is not None or max_price is not None:
            price_filter = RangeFilter(
                field="price",
                gte=min_price,
                lte=max_price
            )
            return self.add_range_filter(price_filter)
        return self
    
    def add_rating_filter(
        self,
        min_rating: Optional[float] = None
    ) -> 'ProductSearchQueryBuilder':
        """Add minimum rating filter"""
        if min_rating is not None:
            rating_filter = RangeFilter(
                field="average_rating",
                gte=min_rating
            )
            return self.add_range_filter(rating_filter)
        return self
    
    def add_stock_filter(
        self,
        in_stock_only: bool = False
    ) -> 'ProductSearchQueryBuilder':
        """Add stock availability filter"""
        if in_stock_only:
            stock_filter = RangeFilter(
                field="stock_quantity",
                gt=0
            )
            return self.add_range_filter(stock_filter)
        return self
    
    def add_popularity_boost(
        self,
        boost_factor: float = 1.5
    ) -> 'ProductSearchQueryBuilder':
        """Add popularity-based scoring boost"""
        popularity_score = FunctionScore(
            function_type="field_value_factor",
            field="total_reviews",
            factor=boost_factor,
            modifier="log1p",
            missing=0
        )
        return self.add_function_score(popularity_score)
    
    def add_rating_boost(
        self,
        boost_factor: float = 2.0
    ) -> 'ProductSearchQueryBuilder':
        """Add rating-based scoring boost"""
        rating_score = FunctionScore(
            function_type="field_value_factor",
            field="average_rating",
            factor=boost_factor,
            modifier="sqrt",
            missing=0
        )
        return self.add_function_score(rating_score)

def create_product_search_query(
    query: str = "",
    categories: List[str] = None,
    brands: List[str] = None,
    min_price: Optional[float] = None,
    max_price: Optional[float] = None,
    min_rating: Optional[float] = None,
    in_stock_only: bool = False,
    sort_by: str = "relevance",
    sort_order: str = "desc",
    page: int = 1,
    per_page: int = 20,
    include_highlights: bool = True
) -> Dict[str, Any]:
    """Create a complete product search query with common parameters"""
    
    builder = ProductSearchQueryBuilder()
    
    # Add text search
    if query:
        builder.add_product_text_search(query)
    
    # Add filters
    builder.add_category_filter(categories or [])
    builder.add_brand_filter(brands or [])
    builder.add_price_range(min_price, max_price)
    builder.add_rating_filter(min_rating)
    builder.add_stock_filter(in_stock_only)
    
    # Add scoring boosts
    builder.add_popularity_boost()
    builder.add_rating_boost()
    
    # Add sorting
    if sort_by == "price":
        sort_field = SortField("price", SortOrder.ASC if sort_order == "asc" else SortOrder.DESC)
        builder.add_sort(sort_field)
    elif sort_by == "rating":
        sort_field = SortField("average_rating", SortOrder.ASC if sort_order == "asc" else SortOrder.DESC, missing="_last")
        builder.add_sort(sort_field)
    elif sort_by == "newest":
        sort_field = SortField("created_at", SortOrder.DESC)
        builder.add_sort(sort_field)
    elif sort_by == "name":
        sort_field = SortField("name.keyword", SortOrder.ASC if sort_order == "asc" else SortOrder.DESC)
        builder.add_sort(sort_field)
    # For relevance, don't add explicit sorting (use score)
    
    # Add highlighting
    if include_highlights and query:
        builder.add_highlight(["name", "description", "category", "brand"])
    
    # Set pagination
    builder.set_pagination(page, per_page)
    
    return builder.build()
