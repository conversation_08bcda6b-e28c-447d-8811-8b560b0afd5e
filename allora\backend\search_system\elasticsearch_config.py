"""
Elasticsearch Configuration and Index Management
Handles Elasticsearch connection, index creation, and mapping definitions
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from elasticsearch import Elasticsearch, helpers
from elasticsearch.exceptions import ConnectionError, RequestError, NotFoundError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ElasticsearchConfig:
    """Elasticsearch configuration and connection management"""
    
    def __init__(self):
        # Check if Elasticsearch is enabled
        self.enabled = os.getenv('ELASTICSEARCH_ENABLED', 'true').lower() == 'true'

        # Elasticsearch connection settings
        self.es_host = os.getenv('ELASTICSEARCH_HOST', 'localhost')
        self.es_port = int(os.getenv('ELASTICSEARCH_PORT', 9200))
        self.es_scheme = os.getenv('ELASTICSEARCH_SCHEME', 'http')
        self.es_username = os.getenv('ELASTICSEARCH_USERNAME', '')
        self.es_password = os.getenv('ELASTICSEARCH_PASSWORD', '')
        
        # Index settings
        self.product_index = 'allora_products'
        self.search_analytics_index = 'allora_search_analytics'
        self.suggestions_index = 'allora_suggestions'
        
        # Initialize Elasticsearch client (optional)
        self.es_client = self._create_client()
        
    def _create_client(self) -> Optional[Elasticsearch]:
        """Create and configure Elasticsearch client"""
        # Check if Elasticsearch is enabled
        if not self.enabled:
            logger.info("Elasticsearch is disabled - running without search functionality")
            return None

        try:
            # Build connection configuration
            config = {
                'hosts': [{'host': self.es_host, 'port': self.es_port, 'scheme': self.es_scheme}],
                'request_timeout': 30,
                'max_retries': 3,
                'retry_on_timeout': True
            }

            # Add authentication if provided
            if self.es_username and self.es_password:
                config['basic_auth'] = (self.es_username, self.es_password)

            client = Elasticsearch(**config)

            # Test connection
            if client.ping():
                logger.info("Successfully connected to Elasticsearch")
                return client
            else:
                logger.warning("Failed to connect to Elasticsearch - running without search functionality")
                return None

        except Exception as e:
            logger.warning(f"Error creating Elasticsearch client: {e} - running without search functionality")
            return None
    
    def get_client(self) -> Optional[Elasticsearch]:
        """Get Elasticsearch client instance"""
        return self.es_client
    
    def get_product_index_mapping(self) -> Dict[str, Any]:
        """Get comprehensive product index mapping"""
        return {
            "settings": {
                "number_of_shards": 2,
                "number_of_replicas": 1,
                "analysis": {
                    "analyzer": {
                        "product_name_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": [
                                "lowercase",
                                "asciifolding",
                                "product_synonym_filter",
                                "product_stemmer"
                            ]
                        },
                        "product_search_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": [
                                "lowercase",
                                "asciifolding",
                                "product_synonym_filter"
                            ]
                        },
                        "autocomplete_analyzer": {
                            "type": "custom",
                            "tokenizer": "keyword",
                            "filter": [
                                "lowercase",
                                "asciifolding",
                                "autocomplete_filter"
                            ]
                        },
                        "autocomplete_search_analyzer": {
                            "type": "custom",
                            "tokenizer": "keyword",
                            "filter": [
                                "lowercase",
                                "asciifolding"
                            ]
                        }
                    },
                    "filter": {
                        "product_synonym_filter": {
                            "type": "synonym",
                            "synonyms": [
                                "eco,green,sustainable,organic,natural",
                                "shirt,tshirt,t-shirt,top",
                                "pants,trousers,jeans",
                                "shoes,footwear,sneakers",
                                "bag,purse,handbag,backpack"
                            ]
                        },
                        "product_stemmer": {
                            "type": "stemmer",
                            "language": "english"
                        },
                        "autocomplete_filter": {
                            "type": "edge_ngram",
                            "min_gram": 2,
                            "max_gram": 20
                        }
                    }
                }
            },
            "mappings": {
                "properties": {
                    # Basic product information
                    "id": {"type": "integer"},
                    "name": {
                        "type": "text",
                        "analyzer": "product_name_analyzer",
                        "search_analyzer": "product_search_analyzer",
                        "fields": {
                            "keyword": {"type": "keyword"},
                            "autocomplete": {
                                "type": "text",
                                "analyzer": "autocomplete_analyzer",
                                "search_analyzer": "autocomplete_search_analyzer"
                            }
                        }
                    },
                    "description": {
                        "type": "text",
                        "analyzer": "product_name_analyzer",
                        "search_analyzer": "product_search_analyzer"
                    },
                    "category": {
                        "type": "keyword",
                        "fields": {
                            "text": {
                                "type": "text",
                                "analyzer": "product_name_analyzer"
                            }
                        }
                    },
                    "brand": {
                        "type": "keyword",
                        "fields": {
                            "text": {
                                "type": "text",
                                "analyzer": "product_name_analyzer"
                            }
                        }
                    },
                    "tags": {
                        "type": "keyword",
                        "fields": {
                            "text": {
                                "type": "text",
                                "analyzer": "product_name_analyzer"
                            }
                        }
                    },
                    
                    # Pricing and availability
                    "price": {"type": "float"},
                    "original_price": {"type": "float"},
                    "discount_percentage": {"type": "float"},
                    "currency": {"type": "keyword"},
                    "stock_quantity": {"type": "integer"},
                    "is_in_stock": {"type": "boolean"},
                    "low_stock_threshold": {"type": "integer"},
                    
                    # Ratings and reviews
                    "average_rating": {"type": "float"},
                    "total_reviews": {"type": "integer"},
                    "rating_distribution": {
                        "type": "object",
                        "properties": {
                            "1_star": {"type": "integer"},
                            "2_star": {"type": "integer"},
                            "3_star": {"type": "integer"},
                            "4_star": {"type": "integer"},
                            "5_star": {"type": "integer"}
                        }
                    },
                    
                    # Sustainability and eco-friendly features
                    "sustainability_score": {"type": "integer"},
                    "eco_certifications": {"type": "keyword"},
                    "carbon_footprint": {"type": "float"},
                    "recyclable": {"type": "boolean"},
                    "organic": {"type": "boolean"},
                    "fair_trade": {"type": "boolean"},
                    
                    # Seller information
                    "seller_id": {"type": "integer"},
                    "seller_name": {
                        "type": "keyword",
                        "fields": {
                            "text": {
                                "type": "text",
                                "analyzer": "product_name_analyzer"
                            }
                        }
                    },
                    "seller_rating": {"type": "float"},
                    "seller_verified": {"type": "boolean"},
                    "store_name": {"type": "keyword"},
                    
                    # Product attributes and variants
                    "attributes": {
                        "type": "nested",
                        "properties": {
                            "name": {"type": "keyword"},
                            "value": {"type": "keyword"},
                            "display_name": {"type": "text"}
                        }
                    },
                    "variants": {
                        "type": "nested",
                        "properties": {
                            "id": {"type": "integer"},
                            "type": {"type": "keyword"},
                            "value": {"type": "keyword"},
                            "price_adjustment": {"type": "float"},
                            "stock_quantity": {"type": "integer"}
                        }
                    },
                    
                    # Images and media
                    "primary_image": {"type": "keyword"},
                    "image_urls": {"type": "keyword"},
                    "has_video": {"type": "boolean"},
                    
                    # Sales and popularity metrics
                    "total_sales": {"type": "integer"},
                    "sales_rank": {"type": "integer"},
                    "popularity_score": {"type": "float"},
                    "trending_score": {"type": "float"},
                    "view_count": {"type": "integer"},
                    "wishlist_count": {"type": "integer"},
                    
                    # Shipping and delivery
                    "free_shipping": {"type": "boolean"},
                    "shipping_weight": {"type": "float"},
                    "shipping_dimensions": {
                        "type": "object",
                        "properties": {
                            "length": {"type": "float"},
                            "width": {"type": "float"},
                            "height": {"type": "float"}
                        }
                    },
                    "estimated_delivery_days": {"type": "integer"},
                    
                    # Temporal data
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "last_indexed_at": {"type": "date"},
                    
                    # Search optimization
                    "search_keywords": {"type": "text", "analyzer": "product_name_analyzer"},
                    "boost_score": {"type": "float"},
                    "featured": {"type": "boolean"},
                    "promoted": {"type": "boolean"},
                    
                    # Location-based data
                    "origin_country": {"type": "keyword"},
                    "available_regions": {"type": "keyword"},
                    
                    # Advanced features
                    "ai_generated_tags": {"type": "keyword"},
                    "visual_features": {
                        "type": "dense_vector",
                        "dims": 1280  # MobileNetV2 feature vector size
                    }
                }
            }
        }
    
    def get_search_analytics_mapping(self) -> Dict[str, Any]:
        """Get search analytics index mapping"""
        return {
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 1
            },
            "mappings": {
                "properties": {
                    "query": {"type": "keyword"},
                    "normalized_query": {"type": "keyword"},
                    "user_id": {"type": "integer"},
                    "session_id": {"type": "keyword"},
                    "guest_session_id": {"type": "keyword"},
                    "results_count": {"type": "integer"},
                    "clicked_results": {"type": "integer"},
                    "filters_applied": {"type": "object"},
                    "sort_by": {"type": "keyword"},
                    "page": {"type": "integer"},
                    "response_time_ms": {"type": "integer"},
                    "timestamp": {"type": "date"},
                    "user_agent": {"type": "text"},
                    "ip_address": {"type": "ip"},
                    "search_type": {"type": "keyword"},  # text, visual, voice, etc.
                    "conversion": {"type": "boolean"},  # Did user make a purchase
                    "exit_page": {"type": "boolean"}  # Did user leave after search
                }
            }
        }
    
    def get_suggestions_mapping(self) -> Dict[str, Any]:
        """Get search suggestions index mapping"""
        return {
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 1,
                "analysis": {
                    "analyzer": {
                        "suggestion_analyzer": {
                            "type": "custom",
                            "tokenizer": "keyword",
                            "filter": ["lowercase", "asciifolding"]
                        }
                    }
                }
            },
            "mappings": {
                "properties": {
                    "text": {
                        "type": "completion",
                        "analyzer": "suggestion_analyzer",
                        "contexts": [
                            {
                                "name": "category",
                                "type": "category"
                            }
                        ]
                    },
                    "type": {"type": "keyword"},  # product, category, brand
                    "weight": {"type": "integer"},
                    "payload": {"type": "object"},
                    "created_at": {"type": "date"}
                }
            }
        }

# Global configuration instance
es_config = ElasticsearchConfig()

def get_elasticsearch_client() -> Optional[Elasticsearch]:
    """Get global Elasticsearch client instance"""
    return es_config.get_client()

def get_product_index_name() -> str:
    """Get product index name"""
    return es_config.product_index

def get_search_analytics_index_name() -> str:
    """Get search analytics index name"""
    return es_config.search_analytics_index

def get_suggestions_index_name() -> str:
    """Get suggestions index name"""
    return es_config.suggestions_index
