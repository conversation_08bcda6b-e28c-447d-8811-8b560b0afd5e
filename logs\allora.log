2025-07-09 20:24:32 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-09 20:24:32 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-09 20:24:32 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-09 20:24:32 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-09 20:24:32 - startup - INFO - ============================================================
2025-07-09 20:24:32 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-09 20:24:32 - startup - INFO - ============================================================
2025-07-09 20:24:32 - startup - INFO - 📅 Startup Time: 2025-07-09T20:24:32.906949
2025-07-09 20:24:32 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-09 20:24:32 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project
2025-07-09 20:24:32 - startup - INFO - 🔧 Log Level: INFO
2025-07-09 20:24:32 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-09 20:24:32 - startup - INFO - ============================================================
2025-07-09 20:24:32 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-09 20:24:37 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.072s]
2025-07-09 20:24:37 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-07-09 20:24:37 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 0 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 445, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 276, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001EB806C00D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001EB806C00D0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-09 20:24:41 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.141s]
2025-07-09 20:24:41 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 2 times in a row, putting on 2 second timeout
2025-07-09 20:24:41 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 1 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 445, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 276, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001EB806C0490>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001EB806C0490>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-09 20:24:41 - elastic_transport.node_pool - INFO - Resurrected node <Urllib3HttpNode(http://localhost:9200)> (force=False)
2025-07-09 20:24:45 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.060s]
2025-07-09 20:24:45 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 3 times in a row, putting on 4 second timeout
2025-07-09 20:24:45 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 2 of 3)
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 445, in request
    self.endheaders()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1271, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 1031, in _send_output
    self.send(msg)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\http\client.py", line 969, in send
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 276, in connect
    self.sock = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000001EB806C0610>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_transport.py", line 342, in perform_request
    resp = node.perform_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\elastic_transport\_node\_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x000001EB806C0610>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it)
2025-07-09 20:24:45 - elastic_transport.node_pool - INFO - Resurrected node <Urllib3HttpNode(http://localhost:9200)> (force=False)
2025-07-09 20:24:49 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:N/A duration:4.084s]
2025-07-09 20:24:49 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://localhost:9200)> has failed for 4 times in a row, putting on 8 second timeout
2025-07-09 20:24:49 - elasticsearch_config - WARNING - Failed to connect to Elasticsearch - running without search functionality
2025-07-09 20:24:49 - allora - INFO - Search API blueprint registered successfully
2025-07-09 20:24:49 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-09 20:24:50 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-09 20:24:50 - allora - INFO - Behavior tracker initialized successfully
2025-07-09 20:24:50 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-09 20:24:50 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-09 20:24:50 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-09 20:24:50 - allora - INFO - RMA API blueprint registered successfully
2025-07-09 20:24:50 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-09 20:24:50 - tracking_system - INFO - Real-time tracking system started
2025-07-09 20:24:50 - allora - INFO - Real-time tracking system initialized successfully
2025-07-09 20:24:50 - notification_service - INFO - Notification delivery service started
2025-07-09 20:24:50 - allora - INFO - Notification service initialized successfully
2025-07-09 20:24:50 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-09 20:24:50 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 19:06:41 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 19:06:41 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 19:06:41 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 19:06:41 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 19:06:41 - startup - INFO - ============================================================
2025-07-11 19:06:41 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 19:06:41 - startup - INFO - ============================================================
2025-07-11 19:06:41 - startup - INFO - 📅 Startup Time: 2025-07-11T19:06:41.280298
2025-07-11 19:06:41 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 19:06:41 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project
2025-07-11 19:06:41 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 19:06:41 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 19:06:41 - startup - INFO - ============================================================
2025-07-11 19:06:41 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 19:06:41 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-11 19:06:41 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 19:06:41 - allora - INFO - Search API blueprint registered successfully
2025-07-11 19:06:41 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 19:06:42 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 19:06:42 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 19:06:42 - tracking_system - INFO - Real-time tracking system started
2025-07-11 19:06:42 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 19:06:42 - notification_service - INFO - Notification delivery service started
2025-07-11 19:06:42 - allora - INFO - Notification service initialized successfully
2025-07-11 19:06:42 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 19:06:42 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-11 19:07:59 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-11 19:07:59 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-11 19:07:59 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-11 19:07:59 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-11 19:07:59 - startup - INFO - ============================================================
2025-07-11 19:07:59 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-11 19:07:59 - startup - INFO - ============================================================
2025-07-11 19:07:59 - startup - INFO - 📅 Startup Time: 2025-07-11T19:07:59.441241
2025-07-11 19:07:59 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-11 19:07:59 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project
2025-07-11 19:07:59 - startup - INFO - 🔧 Log Level: INFO
2025-07-11 19:07:59 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-11 19:07:59 - startup - INFO - ============================================================
2025-07-11 19:07:59 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-11 19:07:59 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.009s]
2025-07-11 19:07:59 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-11 19:08:00 - allora - INFO - Search API blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - Behavior tracker initialized successfully
2025-07-11 19:08:00 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - RMA API blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - Home Content API blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-11 19:08:00 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-11 19:08:00 - tracking_system - INFO - Real-time tracking system started
2025-07-11 19:08:00 - allora - INFO - Real-time tracking system initialized successfully
2025-07-11 19:08:00 - notification_service - INFO - Notification delivery service started
2025-07-11 19:08:00 - allora - INFO - Notification service initialized successfully
2025-07-11 19:08:00 - allora - INFO - Loaded 20 image features and 20 product IDs from image_features.pkl
2025-07-11 19:08:00 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 00:26:56 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 00:26:56 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 00:26:56 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 00:26:56 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 00:26:56 - startup - INFO - ============================================================
2025-07-12 00:26:56 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 00:26:56 - startup - INFO - ============================================================
2025-07-12 00:26:56 - startup - INFO - 📅 Startup Time: 2025-07-12T00:26:56.315217
2025-07-12 00:26:56 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 00:26:56 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project
2025-07-12 00:26:56 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 00:26:56 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 00:26:56 - startup - INFO - ============================================================
2025-07-12 00:26:56 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 00:26:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.108s]
2025-07-12 00:26:57 - elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 00:26:57 - allora - INFO - Search API blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 00:26:57 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - Smart Discovery API blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - Production Smart Features API blueprint registered successfully
2025-07-12 00:26:57 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 00:26:57 - tracking_system - INFO - Real-time tracking system started
2025-07-12 00:26:57 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 00:26:57 - notification_service - INFO - Notification delivery service started
2025-07-12 00:26:57 - allora - INFO - Notification service initialized successfully
2025-07-12 00:26:57 - allora - ERROR - Error loading ML models: [Errno 2] No such file or directory: 'c:\\Users\\<USER>\\Desktop\\allora_project\\allora\\backend\\models\\Recommendation Model\\pkl\\recommendation_model.pkl'
2025-07-12 22:59:28 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 22:59:28 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 22:59:28 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 22:59:28 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 22:59:28 - startup - INFO - ============================================================
2025-07-12 22:59:28 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 22:59:28 - startup - INFO - ============================================================
2025-07-12 22:59:28 - startup - INFO - 📅 Startup Time: 2025-07-12T22:59:28.555865
2025-07-12 22:59:28 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 22:59:28 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project
2025-07-12 22:59:28 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 22:59:28 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 22:59:28 - startup - INFO - ============================================================
2025-07-12 22:59:28 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 22:59:28 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.010s]
2025-07-12 22:59:28 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 22:59:28 - allora - INFO - Search API blueprint registered successfully
2025-07-12 22:59:28 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 22:59:29 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 22:59:29 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 22:59:29 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 22:59:29 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 22:59:29 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 22:59:29 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 22:59:29 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 22:59:29 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 22:59:29 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 22:59:29 - allora - INFO - Recommendation system initialized successfully
2025-07-12 22:59:29 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 22:59:29 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 22:59:29 - tracking_system - INFO - Real-time tracking system started
2025-07-12 22:59:29 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 22:59:29 - notification_service - INFO - Notification delivery service started
2025-07-12 22:59:29 - allora - INFO - Notification service initialized successfully
2025-07-12 22:59:29 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 22:59:29 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 22:59:29 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.013s]
2025-07-12 22:59:29 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.007s]
2025-07-12 23:00:47 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 23:00:47 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 23:00:47 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 23:00:47 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 23:00:47 - startup - INFO - ============================================================
2025-07-12 23:00:47 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 23:00:47 - startup - INFO - ============================================================
2025-07-12 23:00:47 - startup - INFO - 📅 Startup Time: 2025-07-12T23:00:47.377566
2025-07-12 23:00:47 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 23:00:47 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project
2025-07-12 23:00:47 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 23:00:47 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 23:00:47 - startup - INFO - ============================================================
2025-07-12 23:00:47 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 23:00:47 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-12 23:00:47 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 23:00:47 - allora - INFO - Search API blueprint registered successfully
2025-07-12 23:00:47 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 23:00:48 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 23:00:48 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 23:00:48 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 23:00:48 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 23:00:48 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 23:00:48 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 23:00:48 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 23:00:48 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 23:00:48 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 23:00:48 - allora - INFO - Recommendation system initialized successfully
2025-07-12 23:00:48 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 23:00:48 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 23:00:48 - tracking_system - INFO - Real-time tracking system started
2025-07-12 23:00:48 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 23:00:48 - notification_service - INFO - Notification delivery service started
2025-07-12 23:00:48 - allora - INFO - Notification service initialized successfully
2025-07-12 23:00:48 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 23:00:48 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 23:00:48 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.009s]
2025-07-12 23:00:48 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.008s]
2025-07-12 23:01:56 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 23:01:56 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 23:01:56 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 23:01:56 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 23:01:56 - startup - INFO - ============================================================
2025-07-12 23:01:56 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 23:01:56 - startup - INFO - ============================================================
2025-07-12 23:01:56 - startup - INFO - 📅 Startup Time: 2025-07-12T23:01:56.942573
2025-07-12 23:01:56 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 23:01:56 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project
2025-07-12 23:01:56 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 23:01:56 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 23:01:56 - startup - INFO - ============================================================
2025-07-12 23:01:56 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 23:01:57 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.006s]
2025-07-12 23:01:57 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 23:01:57 - allora - INFO - Search API blueprint registered successfully
2025-07-12 23:01:57 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 23:01:57 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 23:01:57 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 23:01:57 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 23:01:57 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 23:01:57 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 23:01:57 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 23:01:57 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 23:01:57 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 23:01:57 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 23:01:57 - allora - INFO - Recommendation system initialized successfully
2025-07-12 23:01:57 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 23:01:57 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 23:01:57 - tracking_system - INFO - Real-time tracking system started
2025-07-12 23:01:57 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 23:01:57 - notification_service - INFO - Notification delivery service started
2025-07-12 23:01:57 - allora - INFO - Notification service initialized successfully
2025-07-12 23:01:57 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 23:01:57 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 23:01:58 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.007s]
2025-07-12 23:01:58 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.007s]
2025-07-12 23:02:34 - allora - INFO - ✅ File logging enabled: logs/allora.log
2025-07-12 23:02:34 - allora - INFO - ✅ Error logging enabled: logs/allora_errors.log
2025-07-12 23:02:34 - allora - INFO - ✅ Performance logging enabled: logs/allora_performance.log
2025-07-12 23:02:34 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-12 23:02:34 - startup - INFO - ============================================================
2025-07-12 23:02:34 - startup - INFO - 🚀 ALLORA E-COMMERCE PLATFORM STARTING
2025-07-12 23:02:34 - startup - INFO - ============================================================
2025-07-12 23:02:34 - startup - INFO - 📅 Startup Time: 2025-07-12T23:02:34.845950
2025-07-12 23:02:34 - startup - INFO - 🐍 Python Version: 3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit (AMD64)]
2025-07-12 23:02:34 - startup - INFO - 📁 Working Directory: C:\Users\<USER>\Desktop\allora_project
2025-07-12 23:02:34 - startup - INFO - 🔧 Log Level: INFO
2025-07-12 23:02:34 - startup - INFO - 📝 Log File: logs/allora.log
2025-07-12 23:02:34 - startup - INFO - ============================================================
2025-07-12 23:02:34 - allora - INFO - ✅ Sentry initialized for Flask backend (Environment: production)
2025-07-12 23:02:35 - elastic_transport.transport - INFO - HEAD http://localhost:9200/ [status:200 duration:0.005s]
2025-07-12 23:02:35 - search_system.elasticsearch_config - INFO - Successfully connected to Elasticsearch
2025-07-12 23:02:35 - allora - INFO - Search API blueprint registered successfully
2025-07-12 23:02:35 - allora - INFO - Search Analytics API blueprint registered successfully
2025-07-12 23:02:35 - allora - INFO - User Behavior API blueprint registered successfully
2025-07-12 23:02:35 - allora - INFO - Behavior tracker initialized successfully
2025-07-12 23:02:35 - allora - INFO - Order Fulfillment API blueprint registered successfully
2025-07-12 23:02:35 - allora - INFO - Webhook handlers blueprint registered successfully
2025-07-12 23:02:35 - allora - INFO - Tracking dashboard blueprint registered successfully
2025-07-12 23:02:35 - allora - INFO - RMA API blueprint registered successfully
2025-07-12 23:02:35 - allora - INFO - Sustainability API blueprint registered successfully
2025-07-12 23:02:35 - allora - INFO - Recommendation API blueprint registered successfully
2025-07-12 23:02:35 - models.RecommendationModel.recommendation_api - WARNING - Failed to initialize database models for recommendation system
2025-07-12 23:02:35 - allora - INFO - Recommendation system initialized successfully
2025-07-12 23:02:35 - allora - INFO - Community Highlights API blueprint registered successfully
2025-07-12 23:02:35 - allora - INFO - Scheduler management blueprint registered successfully
2025-07-12 23:02:35 - tracking_system - INFO - Real-time tracking system started
2025-07-12 23:02:35 - allora - INFO - Real-time tracking system initialized successfully
2025-07-12 23:02:35 - notification_service - INFO - Notification delivery service started
2025-07-12 23:02:35 - allora - INFO - Notification service initialized successfully
2025-07-12 23:02:35 - allora - INFO - Loaded 100 image features and 100 product IDs from image_features.pkl
2025-07-12 23:02:35 - allora - INFO - Successfully loaded ML models (TensorFlow model will be loaded on demand)
2025-07-12 23:02:35 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.006s]
2025-07-12 23:02:35 - elastic_transport.transport - INFO - POST http://localhost:9200/allora_products/_search [status:200 duration:0.009s]
