#!/usr/bin/env python3
"""
Fix Remaining Database Issues Script
===================================

This script fixes the remaining 21 minor issues found in the database:
1. Missing created_at columns (19 tables)
2. Order table index issues (2 items - MySQL reserved word)

These fixes are important for:
- Audit trails and data tracking
- Performance optimization
- Production readiness
- Data consistency

Usage:
    python fix_remaining_database_issues.py [--backup]
"""

import os
import sys
import argparse
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from sqlalchemy import text

class DatabaseFixer:
    def __init__(self):
        self.fixes_applied = []
        self.fixes_failed = []
        
    def log_fix(self, table, description, sql):
        """Log a successful fix"""
        self.fixes_applied.append({
            'table': table,
            'description': description,
            'sql': sql,
            'timestamp': datetime.now()
        })
    
    def log_failure(self, table, description, error):
        """Log a failed fix"""
        self.fixes_failed.append({
            'table': table,
            'description': description,
            'error': str(error),
            'timestamp': datetime.now()
        })

    def fix_missing_created_at_columns(self, connection):
        """Fix missing created_at columns in tables"""
        print("🔧 FIXING MISSING CREATED_AT COLUMNS")
        print("=" * 45)
        
        # Tables that need created_at columns based on the test results
        tables_needing_created_at = [
            'chat_session', 'cookie_consent', 'coupon_usage', 'inventory_sync_log',
            'order_item', 'payment_transaction', 'price_history', 'product_variant',
            'recently_viewed', 'refund', 'rma_approval', 'rma_document', 'sales',
            'search_clicks', 'search_conversions', 'user_interaction_logs',
            'user_sessions', 'visual_search_analytics', 'wishlist'
        ]
        
        fixes_applied = 0
        fixes_failed = 0
        
        for table_name in tables_needing_created_at:
            try:
                # Check if table exists
                result = connection.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                if not result.fetchone():
                    print(f"  ⚠️  {table_name}: Table not found, skipping")
                    continue
                
                # Check if created_at column already exists
                result = connection.execute(text(f"SHOW COLUMNS FROM {table_name} LIKE 'created_at'"))
                if result.fetchone():
                    print(f"  ✅ {table_name}: created_at column already exists")
                    continue
                
                # Add created_at column
                alter_sql = f"""
                ALTER TABLE {table_name} 
                ADD COLUMN created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
                """
                
                connection.execute(text(alter_sql))
                connection.commit()
                
                self.log_fix(table_name, "Added created_at column", alter_sql.strip())
                print(f"  ✅ {table_name}: Added created_at column")
                fixes_applied += 1
                
            except Exception as e:
                self.log_failure(table_name, "Failed to add created_at column", str(e))
                print(f"  ❌ {table_name}: Failed - {str(e)}")
                fixes_failed += 1
        
        print(f"\n📊 CREATED_AT FIXES SUMMARY:")
        print(f"  ✅ Successful: {fixes_applied}")
        print(f"  ❌ Failed: {fixes_failed}")
        
        return fixes_failed == 0

    def fix_order_table_indexes(self, connection):
        """Fix order table index issues (MySQL reserved word)"""
        print("\n🔧 FIXING ORDER TABLE INDEX ISSUES")
        print("=" * 45)
        
        fixes_applied = 0
        fixes_failed = 0
        
        # Check if 'order' table exists
        try:
            result = connection.execute(text("SHOW TABLES LIKE 'order'"))
            if not result.fetchone():
                print("  ⚠️  'order' table not found, skipping")
                return True
            
            # Fix 1: Add index on status column (using backticks for reserved word)
            try:
                # Check if index already exists
                result = connection.execute(text("SHOW INDEX FROM `order` WHERE Key_name = 'idx_order_status'"))
                if not result.fetchone():
                    index_sql = "CREATE INDEX idx_order_status ON `order`(status)"
                    connection.execute(text(index_sql))
                    connection.commit()
                    
                    self.log_fix('order', "Added status column index", index_sql)
                    print(f"  ✅ order: Added status column index")
                    fixes_applied += 1
                else:
                    print(f"  ✅ order: status index already exists")
                    
            except Exception as e:
                self.log_failure('order', "Failed to add status index", str(e))
                print(f"  ❌ order: Failed to add status index - {str(e)}")
                fixes_failed += 1
            
            # Fix 2: Add index on created_at column (using backticks for reserved word)
            try:
                # Check if index already exists
                result = connection.execute(text("SHOW INDEX FROM `order` WHERE Key_name = 'idx_order_created_at'"))
                if not result.fetchone():
                    index_sql = "CREATE INDEX idx_order_created_at ON `order`(created_at)"
                    connection.execute(text(index_sql))
                    connection.commit()
                    
                    self.log_fix('order', "Added created_at column index", index_sql)
                    print(f"  ✅ order: Added created_at column index")
                    fixes_applied += 1
                else:
                    print(f"  ✅ order: created_at index already exists")
                    
            except Exception as e:
                self.log_failure('order', "Failed to add created_at index", str(e))
                print(f"  ❌ order: Failed to add created_at index - {str(e)}")
                fixes_failed += 1
        
        except Exception as e:
            self.log_failure('order', "Failed to access order table", str(e))
            print(f"  ❌ order: Failed to access table - {str(e)}")
            fixes_failed += 1
        
        print(f"\n📊 ORDER TABLE FIXES SUMMARY:")
        print(f"  ✅ Successful: {fixes_applied}")
        print(f"  ❌ Failed: {fixes_failed}")
        
        return fixes_failed == 0

    def add_optional_updated_at_columns(self, connection):
        """Add updated_at columns to important tables (optional enhancement)"""
        print("\n🔧 ADDING OPTIONAL UPDATED_AT COLUMNS")
        print("=" * 45)
        
        # Important tables that would benefit from updated_at columns
        important_tables = [
            'users', 'products', 'orders', 'order_item', 'cart_item',
            'payment_transaction', 'support_ticket', 'rma_request',
            'product_review', 'wishlist', 'user_address'
        ]
        
        fixes_applied = 0
        fixes_failed = 0
        
        for table_name in important_tables:
            try:
                # Check if table exists
                result = connection.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                if not result.fetchone():
                    continue
                
                # Check if updated_at column already exists
                result = connection.execute(text(f"SHOW COLUMNS FROM {table_name} LIKE 'updated_at'"))
                if result.fetchone():
                    continue
                
                # Add updated_at column
                alter_sql = f"""
                ALTER TABLE {table_name} 
                ADD COLUMN updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                """
                
                connection.execute(text(alter_sql))
                connection.commit()
                
                self.log_fix(table_name, "Added updated_at column", alter_sql.strip())
                print(f"  ✅ {table_name}: Added updated_at column")
                fixes_applied += 1
                
            except Exception as e:
                # This is optional, so don't count as critical failure
                print(f"  ⚠️  {table_name}: Could not add updated_at - {str(e)}")
        
        print(f"\n📊 UPDATED_AT ENHANCEMENTS:")
        print(f"  ✅ Added: {fixes_applied}")
        
        return True

    def create_backup(self, connection):
        """Create a backup before making changes"""
        print("💾 CREATING BACKUP")
        print("=" * 25)
        
        try:
            backup_filename = f"database_backup_before_fixes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            
            # Get list of all tables
            result = connection.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result.fetchall()]
            
            backup_content = [
                f"-- Database Backup Before Fixes",
                f"-- Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"-- Tables: {len(tables)}",
                f"",
                f"USE allora_db;",
                f""
            ]
            
            # Export table structures
            for table_name in tables:
                try:
                    result = connection.execute(text(f"SHOW CREATE TABLE `{table_name}`"))
                    table_create = result.fetchone()
                    
                    backup_content.extend([
                        f"-- Table: {table_name}",
                        str(table_create[1]) + ";",
                        ""
                    ])
                except Exception as e:
                    backup_content.append(f"-- ERROR: Could not backup table {table_name}: {str(e)}")
            
            # Write backup file
            with open(backup_filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(backup_content))
            
            print(f"✅ Backup created: {backup_filename}")
            return backup_filename
            
        except Exception as e:
            print(f"❌ Backup failed: {str(e)}")
            return None

    def verify_fixes(self, connection):
        """Verify that fixes were applied correctly"""
        print("\n✅ VERIFYING FIXES")
        print("=" * 25)
        
        verification_passed = 0
        verification_failed = 0
        
        # Verify created_at columns
        tables_with_created_at = [
            'chat_session', 'cookie_consent', 'coupon_usage', 'inventory_sync_log',
            'order_item', 'payment_transaction', 'price_history', 'product_variant',
            'recently_viewed', 'refund', 'rma_approval', 'rma_document', 'sales',
            'search_clicks', 'search_conversions', 'user_interaction_logs',
            'user_sessions', 'visual_search_analytics', 'wishlist'
        ]
        
        for table_name in tables_with_created_at:
            try:
                result = connection.execute(text(f"SHOW TABLES LIKE '{table_name}'"))
                if not result.fetchone():
                    continue
                    
                result = connection.execute(text(f"SHOW COLUMNS FROM {table_name} LIKE 'created_at'"))
                if result.fetchone():
                    verification_passed += 1
                else:
                    verification_failed += 1
                    print(f"  ❌ {table_name}: created_at column still missing")
                    
            except Exception as e:
                verification_failed += 1
                print(f"  ❌ {table_name}: Verification error - {str(e)}")
        
        # Verify order table indexes
        try:
            result = connection.execute(text("SHOW TABLES LIKE 'order'"))
            if result.fetchone():
                result = connection.execute(text("SHOW INDEX FROM `order` WHERE Key_name IN ('idx_order_status', 'idx_order_created_at')"))
                indexes = result.fetchall()
                
                if len(indexes) >= 2:
                    verification_passed += 1
                    print(f"  ✅ order: Indexes verified")
                else:
                    verification_failed += 1
                    print(f"  ❌ order: Missing indexes")
                    
        except Exception as e:
            verification_failed += 1
            print(f"  ❌ order: Index verification error - {str(e)}")
        
        print(f"\n📊 VERIFICATION RESULTS:")
        print(f"  ✅ Passed: {verification_passed}")
        print(f"  ❌ Failed: {verification_failed}")
        
        return verification_failed == 0

    def generate_summary_report(self):
        """Generate a summary report of all fixes"""
        print(f"\n📋 COMPREHENSIVE FIX REPORT")
        print("=" * 50)
        
        if self.fixes_applied:
            print(f"\n✅ FIXES SUCCESSFULLY APPLIED ({len(self.fixes_applied)}):")
            for fix in self.fixes_applied:
                print(f"  • {fix['table']}: {fix['description']}")
                print(f"    Time: {fix['timestamp'].strftime('%H:%M:%S')}")
        
        if self.fixes_failed:
            print(f"\n❌ FIXES THAT FAILED ({len(self.fixes_failed)}):")
            for fix in self.fixes_failed:
                print(f"  • {fix['table']}: {fix['description']}")
                print(f"    Error: {fix['error']}")
                print(f"    Time: {fix['timestamp'].strftime('%H:%M:%S')}")
        
        total_attempted = len(self.fixes_applied) + len(self.fixes_failed)
        success_rate = (len(self.fixes_applied) / total_attempted * 100) if total_attempted > 0 else 100
        
        print(f"\n📊 OVERALL STATISTICS:")
        print(f"  Total Fixes Attempted: {total_attempted}")
        print(f"  Successful Fixes: {len(self.fixes_applied)}")
        print(f"  Failed Fixes: {len(self.fixes_failed)}")
        print(f"  Success Rate: {success_rate:.1f}%")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Fix remaining database issues')
    parser.add_argument('--backup', action='store_true', help='Create backup before fixes')
    
    args = parser.parse_args()
    
    print("🔧 FIXING REMAINING DATABASE ISSUES")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    fixer = DatabaseFixer()
    success = True
    
    with app.app_context():
        try:
            with db.engine.connect() as connection:
                # Step 1: Create backup if requested
                if args.backup:
                    backup_file = fixer.create_backup(connection)
                    if not backup_file:
                        print("❌ Backup failed, aborting for safety")
                        return False
                
                # Step 2: Fix missing created_at columns
                if not fixer.fix_missing_created_at_columns(connection):
                    success = False
                
                # Step 3: Fix order table indexes
                if not fixer.fix_order_table_indexes(connection):
                    success = False
                
                # Step 4: Add optional updated_at columns
                fixer.add_optional_updated_at_columns(connection)
                
                # Step 5: Verify fixes
                if not fixer.verify_fixes(connection):
                    success = False
                
                # Step 6: Generate report
                fixer.generate_summary_report()
                
        except Exception as e:
            print(f"❌ Fix execution failed: {e}")
            success = False
    
    if success:
        print(f"\n🎉 SUCCESS: All database issues fixed!")
        print("✅ Database is now fully production-ready")
        print("\n📋 NEXT STEPS:")
        print("1. Run comprehensive database test to verify")
        print("2. Database quality score should be significantly improved")
        print("3. All 21 remaining issues should be resolved")
    else:
        print(f"\n⚠️  PARTIAL SUCCESS: Some issues may remain")
        print("Check the detailed report above for specifics")
    
    print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
