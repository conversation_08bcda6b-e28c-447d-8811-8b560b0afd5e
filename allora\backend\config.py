"""
Configuration Settings for Allora E-commerce Platform
====================================================

This module contains configuration settings for various components of the application,
including search, analytics, database, and external service integrations.

Author: Allora Development Team
Date: 2025-07-07
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Base configuration class"""
    
    # Application Settings
    SECRET_KEY = os.getenv('SECRET_KEY')
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY')
    
    # Database Configuration
    DATABASE_URL = os.getenv('DATABASE_URL')
    SQLALCHEMY_DATABASE_URI = DATABASE_URL
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'max_overflow': 0
    }
    
    # Elasticsearch Configuration
    ELASTICSEARCH_ENABLED = os.getenv('ELASTICSEARCH_ENABLED', 'true').lower() == 'true'
    ELASTICSEARCH_HOST = os.getenv('ELASTICSEARCH_HOST', 'localhost')
    ELASTICSEARCH_PORT = int(os.getenv('ELASTICSEARCH_PORT', '9200'))
    ELASTICSEARCH_URL = f"http://{ELASTICSEARCH_HOST}:{ELASTICSEARCH_PORT}"
    ELASTICSEARCH_TIMEOUT = int(os.getenv('ELASTICSEARCH_TIMEOUT', '30'))
    ELASTICSEARCH_MAX_RETRIES = int(os.getenv('ELASTICSEARCH_MAX_RETRIES', '3'))
    
    # Search Configuration
    SEARCH_ENABLED = os.getenv('SEARCH_ENABLED', 'true').lower() == 'true'
    SEARCH_FALLBACK_ENABLED = True  # Always enable fallback for reliability
    SEARCH_DEFAULT_PAGE_SIZE = int(os.getenv('SEARCH_DEFAULT_PAGE_SIZE', '20'))
    SEARCH_MAX_PAGE_SIZE = int(os.getenv('SEARCH_MAX_PAGE_SIZE', '100'))
    
    # Search Analytics Configuration
    SEARCH_ANALYTICS_ENABLED = os.getenv('SEARCH_ANALYTICS_ENABLED', 'true').lower() == 'true'
    SEARCH_ANALYTICS_RETENTION_DAYS = int(os.getenv('SEARCH_ANALYTICS_RETENTION_DAYS', '90'))
    SEARCH_ANALYTICS_BATCH_SIZE = int(os.getenv('SEARCH_ANALYTICS_BATCH_SIZE', '100'))
    
    # Redis Configuration (for caching and sessions)
    REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
    REDIS_PORT = int(os.getenv('REDIS_PORT', '6379'))
    REDIS_DB = int(os.getenv('REDIS_DB', '0'))
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', None)
    REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}" if REDIS_PASSWORD else f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

    # Separate Redis databases for different purposes
    SESSION_REDIS = os.getenv('SESSION_REDIS', f"redis://{REDIS_HOST}:{REDIS_PORT}/1")
    CACHE_REDIS = os.getenv('CACHE_REDIS', f"redis://{REDIS_HOST}:{REDIS_PORT}/2")
    RATE_LIMIT_STORAGE_URL = os.getenv('RATE_LIMIT_STORAGE_URL', f"redis://{REDIS_HOST}:{REDIS_PORT}/3")
    
    # Email Configuration
    MAIL_SERVER = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.getenv('MAIL_PORT', '587'))
    MAIL_USE_TLS = os.getenv('MAIL_USE_TLS', 'true').lower() == 'true'
    MAIL_USERNAME = os.getenv('MAIL_USERNAME')
    MAIL_PASSWORD = os.getenv('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.getenv('MAIL_DEFAULT_SENDER', '<EMAIL>')
    
    # OAuth Configuration
    GOOGLE_CLIENT_ID = os.getenv('GOOGLE_CLIENT_ID')
    GOOGLE_CLIENT_SECRET = os.getenv('GOOGLE_CLIENT_SECRET')
    
    # File Upload Configuration
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', 'uploads')
    MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', '16777216'))  # 16MB
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'txt', 'doc', 'docx'}
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO').upper()
    LOG_FILE = os.getenv('LOG_FILE', 'allora.log')
    
    # Rate Limiting Configuration
    RATE_LIMIT_ENABLED = os.getenv('RATE_LIMIT_ENABLED', 'true').lower() == 'true'
    RATE_LIMIT_DEFAULT = os.getenv('RATE_LIMIT_DEFAULT', '100 per hour')
    
    # Security Configuration
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', 'http://localhost:3000').split(',')
    HTTPS_REDIRECT = os.getenv('HTTPS_REDIRECT', 'false').lower() == 'true'
    
    # Payment Gateway Configuration
    RAZORPAY_KEY_ID = os.getenv('RAZORPAY_KEY_ID')
    RAZORPAY_KEY_SECRET = os.getenv('RAZORPAY_KEY_SECRET')

    # SMS Configuration
    SMS_API_KEY = os.getenv('SMS_API_KEY')
    SMS_SENDER_ID = os.getenv('SMS_SENDER_ID', 'ALLORA')

    # Monitoring Configuration
    SENTRY_DSN = os.getenv('SENTRY_DSN')
    NEW_RELIC_LICENSE_KEY = os.getenv('NEW_RELIC_LICENSE_KEY')

    # External API Configuration
    PAYMENT_GATEWAY_API_KEY = os.getenv('PAYMENT_GATEWAY_API_KEY')
    SHIPPING_API_KEY = os.getenv('SHIPPING_API_KEY')
    
    # Feature Flags
    ENABLE_RECOMMENDATIONS = os.getenv('ENABLE_RECOMMENDATIONS', 'true').lower() == 'true'
    ENABLE_ANALYTICS = os.getenv('ENABLE_ANALYTICS', 'true').lower() == 'true'
    ENABLE_NOTIFICATIONS = os.getenv('ENABLE_NOTIFICATIONS', 'true').lower() == 'true'

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    
    # Override for development
    HTTPS_REDIRECT = False
    RATE_LIMIT_ENABLED = False

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Production-specific settings
    HTTPS_REDIRECT = True
    RATE_LIMIT_ENABLED = True
    
    # Stricter security settings
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    
    # Use in-memory database for testing
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # Disable external services for testing
    SEARCH_ENABLED = False
    SEARCH_ANALYTICS_ENABLED = False
    RATE_LIMIT_ENABLED = False

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """Get configuration class based on environment"""
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')
    
    return config.get(config_name, config['default'])

# Search-specific configuration functions
def get_search_config():
    """Get search-specific configuration"""
    cfg = get_config()()
    return {
        'enabled': cfg.SEARCH_ENABLED,
        'elasticsearch_url': cfg.ELASTICSEARCH_URL,
        'elasticsearch_timeout': cfg.ELASTICSEARCH_TIMEOUT,
        'elasticsearch_max_retries': cfg.ELASTICSEARCH_MAX_RETRIES,
        'fallback_enabled': cfg.SEARCH_FALLBACK_ENABLED,
        'default_page_size': cfg.SEARCH_DEFAULT_PAGE_SIZE,
        'max_page_size': cfg.SEARCH_MAX_PAGE_SIZE
    }

def get_analytics_config():
    """Get analytics-specific configuration"""
    cfg = get_config()()
    return {
        'enabled': cfg.SEARCH_ANALYTICS_ENABLED,
        'retention_days': cfg.SEARCH_ANALYTICS_RETENTION_DAYS,
        'batch_size': cfg.SEARCH_ANALYTICS_BATCH_SIZE,
        'elasticsearch_url': cfg.ELASTICSEARCH_URL
    }

# Export commonly used configurations and direct access to common settings
DATABASE_URL = Config.DATABASE_URL
ELASTICSEARCH_URL = Config.ELASTICSEARCH_URL
REDIS_URL = Config.REDIS_URL

__all__ = [
    'Config',
    'DevelopmentConfig',
    'ProductionConfig',
    'TestingConfig',
    'config',
    'get_config',
    'get_search_config',
    'get_analytics_config',
    'DATABASE_URL',
    'ELASTICSEARCH_URL',
    'REDIS_URL'
]
