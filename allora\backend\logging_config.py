"""
Comprehensive Logging Configuration for Allora E-commerce Platform
================================================================

This module provides advanced logging functionality including:
- File and console logging with rotation
- Structured logging with JSON format option
- Performance logging
- Error tracking integration
- Log filtering and formatting

Author: Allora Development Team
Date: 2025-07-08
"""

import os
import sys
import json
import logging
import logging.handlers
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        if hasattr(record, 'ip_address'):
            log_entry['ip_address'] = record.ip_address
        
        return json.dumps(log_entry)


class PerformanceFilter(logging.Filter):
    """Filter for performance-related logs"""
    
    def filter(self, record):
        return hasattr(record, 'performance') and record.performance


class ErrorFilter(logging.Filter):
    """Filter for error-level logs only"""
    
    def filter(self, record):
        return record.levelno >= logging.ERROR


class AlloraLogger:
    """Enhanced logger for Allora application"""
    
    def __init__(self, name: str = 'allora'):
        self.logger = logging.getLogger(name)
        self.setup_complete = False
    
    def setup_logging(self, 
                     log_level: str = 'INFO',
                     log_file: str = 'logs/allora.log',
                     enable_json: bool = False,
                     enable_performance_logging: bool = True) -> logging.Logger:
        """
        Setup comprehensive logging configuration
        
        Args:
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_file: Path to log file
            enable_json: Enable JSON structured logging
            enable_performance_logging: Enable performance logging
        """
        if self.setup_complete:
            return self.logger
        
        # Create logs directory
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        root_logger.handlers.clear()
        
        # Choose formatter
        if enable_json:
            formatter = JSONFormatter()
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        
        # Console Handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # File Handler with rotation
        try:
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            
            self.logger.info(f"✅ File logging enabled: {log_file}")
        except Exception as e:
            self.logger.warning(f"⚠️ Could not setup file logging: {e}")
        
        # Error-only file handler
        try:
            error_file = log_file.replace('.log', '_errors.log')
            error_handler = logging.handlers.RotatingFileHandler(
                error_file,
                maxBytes=5*1024*1024,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(formatter)
            error_handler.addFilter(ErrorFilter())
            root_logger.addHandler(error_handler)
            
            self.logger.info(f"✅ Error logging enabled: {error_file}")
        except Exception as e:
            self.logger.warning(f"⚠️ Could not setup error file logging: {e}")
        
        # Performance logging handler
        if enable_performance_logging:
            try:
                perf_file = log_file.replace('.log', '_performance.log')
                perf_handler = logging.handlers.RotatingFileHandler(
                    perf_file,
                    maxBytes=5*1024*1024,  # 5MB
                    backupCount=2,
                    encoding='utf-8'
                )
                perf_handler.setLevel(logging.INFO)
                perf_handler.setFormatter(formatter)
                perf_handler.addFilter(PerformanceFilter())
                root_logger.addHandler(perf_handler)
                
                self.logger.info(f"✅ Performance logging enabled: {perf_file}")
            except Exception as e:
                self.logger.warning(f"⚠️ Could not setup performance logging: {e}")
        
        self.setup_complete = True
        self.logger.info("🚀 Allora Logging System Initialized")
        return self.logger
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """Log performance metrics"""
        extra = {'performance': True}
        extra.update(kwargs)
        
        self.logger.info(
            f"PERFORMANCE: {operation} completed in {duration:.3f}s",
            extra=extra
        )
    
    def log_user_action(self, user_id: str, action: str, details: Dict[str, Any] = None):
        """Log user actions with context"""
        extra = {'user_id': user_id}
        if details:
            extra.update(details)
        
        self.logger.info(f"USER_ACTION: {action}", extra=extra)
    
    def log_api_request(self, method: str, endpoint: str, user_id: str = None, 
                       ip_address: str = None, duration: float = None):
        """Log API requests"""
        extra = {}
        if user_id:
            extra['user_id'] = user_id
        if ip_address:
            extra['ip_address'] = ip_address
        if duration:
            extra['performance'] = True
        
        message = f"API_REQUEST: {method} {endpoint}"
        if duration:
            message += f" ({duration:.3f}s)"
        
        self.logger.info(message, extra=extra)
    
    def log_database_operation(self, operation: str, table: str, duration: float = None):
        """Log database operations"""
        extra = {}
        if duration:
            extra['performance'] = True
        
        message = f"DB_OPERATION: {operation} on {table}"
        if duration:
            message += f" ({duration:.3f}s)"
        
        self.logger.info(message, extra=extra)


# Global logger instance
allora_logger = AlloraLogger()

def get_logger(name: str = None) -> logging.Logger:
    """Get a logger instance"""
    if name:
        return logging.getLogger(name)
    return allora_logger.logger

def setup_application_logging():
    """Setup logging for the entire application"""
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    log_file = os.getenv('LOG_FILE', 'logs/allora.log')
    enable_json = os.getenv('LOG_FORMAT', 'text').lower() == 'json'
    enable_performance = os.getenv('ENABLE_PERFORMANCE_LOGGING', 'true').lower() == 'true'
    
    return allora_logger.setup_logging(
        log_level=log_level,
        log_file=log_file,
        enable_json=enable_json,
        enable_performance_logging=enable_performance
    )

def log_startup_info():
    """Log application startup information"""
    logger = get_logger('startup')
    logger.info("=" * 60)
    logger.info("🚀 ALLORA E-COMMERCE PLATFORM STARTING")
    logger.info("=" * 60)
    logger.info(f"📅 Startup Time: {datetime.now().isoformat()}")
    logger.info(f"🐍 Python Version: {sys.version}")
    logger.info(f"📁 Working Directory: {os.getcwd()}")
    logger.info(f"🔧 Log Level: {os.getenv('LOG_LEVEL', 'INFO')}")
    logger.info(f"📝 Log File: {os.getenv('LOG_FILE', 'logs/allora.log')}")
    logger.info("=" * 60)


# Decorators for logging
def log_performance(operation_name: str = None):
    """Decorator to log function performance"""
    import time
    import functools

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            operation = operation_name or f"{func.__module__}.{func.__name__}"

            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                allora_logger.log_performance(operation, duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger = get_logger(func.__module__)
                logger.error(f"PERFORMANCE: {operation} failed after {duration:.3f}s - {str(e)}")
                raise

        return wrapper
    return decorator


def log_api_call(func):
    """Decorator to log API calls"""
    import functools
    import time
    from flask import request

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()

        try:
            # Get request info
            method = request.method if request else 'UNKNOWN'
            endpoint = request.endpoint if request else func.__name__
            ip_address = request.remote_addr if request else None

            result = func(*args, **kwargs)
            duration = time.time() - start_time

            allora_logger.log_api_request(method, endpoint, ip_address=ip_address, duration=duration)
            return result

        except Exception as e:
            duration = time.time() - start_time
            logger = get_logger('api')
            logger.error(f"API_ERROR: {method} {endpoint} failed after {duration:.3f}s - {str(e)}")
            raise

    return wrapper
