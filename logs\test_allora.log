2025-07-09 01:54:44 - allora - INFO - ✅ File logging enabled: logs/test_allora.log
2025-07-09 01:54:44 - allora - INFO - ✅ Error logging enabled: logs/test_allora_errors.log
2025-07-09 01:54:44 - allora - INFO - ✅ Performance logging enabled: logs/test_allora_performance.log
2025-07-09 01:54:44 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-09 01:54:44 - test_basic - DEBUG - This is a debug message
2025-07-09 01:54:44 - test_basic - INFO - This is an info message
2025-07-09 01:54:44 - test_basic - WARNING - This is a warning message
2025-07-09 01:54:44 - test_basic - ERROR - This is an error message
2025-07-09 01:54:44 - test_basic - CRITICAL - This is a critical message
2025-07-09 01:54:44 - allora - INFO - PERFORMANCE: test_operation completed in 0.123s
2025-07-09 01:54:44 - allora - INFO - PERFORMANCE: decorated_function completed in 0.105s
2025-07-09 01:54:44 - allora - INFO - USER_ACTION: product_purchase
2025-07-09 01:54:44 - allora - INFO - DB_OPERATION: SELECT on products (0.045s)
2025-07-09 01:54:44 - allora - INFO - DB_OPERATION: INSERT on orders (0.123s)
2025-07-09 01:54:44 - allora - INFO - DB_OPERATION: UPDATE on users
2025-07-09 01:54:44 - allora - INFO - API_REQUEST: GET /api/products (0.234s)
2025-07-09 01:54:44 - allora - INFO - API_REQUEST: POST /api/orders (0.567s)
2025-07-09 01:54:44 - test_error - ERROR - Division by zero error occurred
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\allora_project\allora\backend\test_logging.py", line 129, in test_error_logging
    result = 1 / 0
ZeroDivisionError: division by zero
2025-07-09 01:54:44 - test_error - ERROR - JSON parsing failed
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\allora_project\allora\backend\test_logging.py", line 135, in test_error_logging
    data = json.loads("invalid json")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)
2025-07-09 02:00:47 - allora - INFO - ✅ File logging enabled: logs/test_allora.log
2025-07-09 02:00:47 - allora - INFO - ✅ Error logging enabled: logs/test_allora_errors.log
2025-07-09 02:00:47 - allora - INFO - ✅ Performance logging enabled: logs/test_allora_performance.log
2025-07-09 02:00:47 - allora - INFO - 🚀 Allora Logging System Initialized
2025-07-09 02:00:47 - test_basic - DEBUG - This is a debug message
2025-07-09 02:00:47 - test_basic - INFO - This is an info message
2025-07-09 02:00:47 - test_basic - WARNING - This is a warning message
2025-07-09 02:00:47 - test_basic - ERROR - This is an error message
2025-07-09 02:00:47 - test_basic - CRITICAL - This is a critical message
2025-07-09 02:00:47 - allora - INFO - PERFORMANCE: test_operation completed in 0.123s
2025-07-09 02:00:47 - allora - INFO - PERFORMANCE: decorated_function completed in 0.115s
2025-07-09 02:00:47 - allora - INFO - USER_ACTION: product_purchase
2025-07-09 02:00:47 - allora - INFO - DB_OPERATION: SELECT on products (0.045s)
2025-07-09 02:00:47 - allora - INFO - DB_OPERATION: INSERT on orders (0.123s)
2025-07-09 02:00:47 - allora - INFO - DB_OPERATION: UPDATE on users
2025-07-09 02:00:47 - allora - INFO - API_REQUEST: GET /api/products (0.234s)
2025-07-09 02:00:47 - allora - INFO - API_REQUEST: POST /api/orders (0.567s)
2025-07-09 02:00:47 - test_error - ERROR - Division by zero error occurred
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\allora_project\allora\backend\test_logging.py", line 129, in test_error_logging
    result = 1 / 0
ZeroDivisionError: division by zero
2025-07-09 02:00:47 - test_error - ERROR - JSON parsing failed
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\allora_project\allora\backend\test_logging.py", line 135, in test_error_logging
    data = json.loads("invalid json")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)
