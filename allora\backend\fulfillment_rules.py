"""
Fulfillment Rules Configuration
==============================

Configurable business rules for order fulfillment decisions.
This module provides a flexible system for defining and managing
fulfillment rules that can be customized based on business requirements.

Features:
1. Rule-based fulfillment decisions
2. Configurable thresholds and conditions
3. Priority-based rule evaluation
4. Dynamic rule management
5. Integration with fulfillment engine
"""

from typing import Dict, List, Any, Optional
from enum import Enum
from dataclasses import dataclass
import json
import logging

from order_fulfillment_engine import BusinessRuleType, FulfillmentRule
from carrier_integration import ShippingCarrier

logger = logging.getLogger(__name__)

# ============================================================================
# RULE CONFIGURATION CLASSES
# ============================================================================

class RuleConditionOperator(Enum):
    """Operators for rule conditions"""
    EQUALS = "equals"
    NOT_EQUALS = "not_equals"
    GREATER_THAN = "gt"
    GREATER_THAN_OR_EQUAL = "gte"
    LESS_THAN = "lt"
    LESS_THAN_OR_EQUAL = "lte"
    IN = "in"
    NOT_IN = "not_in"
    CONTAINS = "contains"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"

@dataclass
class RuleCondition:
    """Individual rule condition"""
    field: str
    operator: RuleConditionOperator
    value: Any
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """Evaluate this condition against context"""
        if self.field not in context:
            return False
        
        field_value = context[self.field]
        
        try:
            if self.operator == RuleConditionOperator.EQUALS:
                return field_value == self.value
            elif self.operator == RuleConditionOperator.NOT_EQUALS:
                return field_value != self.value
            elif self.operator == RuleConditionOperator.GREATER_THAN:
                return field_value > self.value
            elif self.operator == RuleConditionOperator.GREATER_THAN_OR_EQUAL:
                return field_value >= self.value
            elif self.operator == RuleConditionOperator.LESS_THAN:
                return field_value < self.value
            elif self.operator == RuleConditionOperator.LESS_THAN_OR_EQUAL:
                return field_value <= self.value
            elif self.operator == RuleConditionOperator.IN:
                return field_value in self.value
            elif self.operator == RuleConditionOperator.NOT_IN:
                return field_value not in self.value
            elif self.operator == RuleConditionOperator.CONTAINS:
                return self.value in str(field_value)
            elif self.operator == RuleConditionOperator.STARTS_WITH:
                return str(field_value).startswith(str(self.value))
            elif self.operator == RuleConditionOperator.ENDS_WITH:
                return str(field_value).endswith(str(self.value))
            else:
                return False
        except Exception as e:
            logger.error(f"Error evaluating condition: {e}")
            return False

@dataclass
class FulfillmentRuleConfig:
    """Configuration for a fulfillment rule"""
    name: str
    description: str
    rule_type: BusinessRuleType
    conditions: List[RuleCondition]
    actions: Dict[str, Any]
    priority: int = 1
    is_active: bool = True
    
    def to_fulfillment_rule(self) -> FulfillmentRule:
        """Convert to FulfillmentRule object"""
        # Convert conditions to simple dict format for compatibility
        condition_dict = {}
        for condition in self.conditions:
            if condition.operator in [RuleConditionOperator.GREATER_THAN_OR_EQUAL, 
                                    RuleConditionOperator.LESS_THAN_OR_EQUAL]:
                if condition.field not in condition_dict:
                    condition_dict[condition.field] = {}
                
                if condition.operator == RuleConditionOperator.GREATER_THAN_OR_EQUAL:
                    condition_dict[condition.field]['min'] = condition.value
                else:
                    condition_dict[condition.field]['max'] = condition.value
            elif condition.operator == RuleConditionOperator.EQUALS:
                condition_dict[condition.field] = condition.value
            elif condition.operator == RuleConditionOperator.IN:
                condition_dict[condition.field] = {'in': condition.value}
        
        return FulfillmentRule(
            rule_type=self.rule_type,
            condition=condition_dict,
            action=self.actions,
            priority=self.priority,
            is_active=self.is_active
        )

# ============================================================================
# PREDEFINED RULE CONFIGURATIONS
# ============================================================================

class DefaultFulfillmentRules:
    """Default fulfillment rules for common scenarios"""
    
    @staticmethod
    def get_auto_fulfill_rules() -> List[FulfillmentRuleConfig]:
        """Rules for automatic fulfillment"""
        return [
            # Auto-fulfill small orders
            FulfillmentRuleConfig(
                name="Auto Fulfill Small Orders",
                description="Automatically fulfill orders under ₹1000",
                rule_type=BusinessRuleType.AUTO_FULFILL,
                conditions=[
                    RuleCondition("order_total", RuleConditionOperator.LESS_THAN_OR_EQUAL, 1000.0)
                ],
                actions={"auto_fulfill": True},
                priority=1
            ),
            
            # Auto-fulfill registered customer orders
            FulfillmentRuleConfig(
                name="Auto Fulfill Registered Customers",
                description="Automatically fulfill orders from registered customers under ₹5000",
                rule_type=BusinessRuleType.AUTO_FULFILL,
                conditions=[
                    RuleCondition("customer_type", RuleConditionOperator.EQUALS, "registered"),
                    RuleCondition("order_total", RuleConditionOperator.LESS_THAN_OR_EQUAL, 5000.0)
                ],
                actions={"auto_fulfill": True},
                priority=2
            )
        ]
    
    @staticmethod
    def get_approval_rules() -> List[FulfillmentRuleConfig]:
        """Rules requiring manual approval"""
        return [
            # Large orders require approval
            FulfillmentRuleConfig(
                name="Large Order Approval",
                description="Orders over ₹10000 require manual approval",
                rule_type=BusinessRuleType.REQUIRE_APPROVAL,
                conditions=[
                    RuleCondition("order_total", RuleConditionOperator.GREATER_THAN, 10000.0)
                ],
                actions={"require_approval": True},
                priority=1
            ),
            
            # International orders require approval
            FulfillmentRuleConfig(
                name="International Order Approval",
                description="International orders require manual approval",
                rule_type=BusinessRuleType.REQUIRE_APPROVAL,
                conditions=[
                    RuleCondition("is_international", RuleConditionOperator.EQUALS, True)
                ],
                actions={"require_approval": True},
                priority=1
            )
        ]
    
    @staticmethod
    def get_carrier_selection_rules() -> List[FulfillmentRuleConfig]:
        """Rules for carrier selection"""
        return [
            # Blue Dart for premium customers
            FulfillmentRuleConfig(
                name="Blue Dart for Premium",
                description="Use Blue Dart for premium customers",
                rule_type=BusinessRuleType.CARRIER_SELECTION,
                conditions=[
                    RuleCondition("customer_type", RuleConditionOperator.EQUALS, "premium")
                ],
                actions={"preferred_carrier": ShippingCarrier.BLUE_DART.value},
                priority=1
            ),
            
            # Delhivery for standard domestic
            FulfillmentRuleConfig(
                name="Delhivery Domestic Standard",
                description="Use Delhivery for standard domestic orders",
                rule_type=BusinessRuleType.CARRIER_SELECTION,
                conditions=[
                    RuleCondition("destination_country", RuleConditionOperator.EQUALS, "India"),
                    RuleCondition("customer_type", RuleConditionOperator.IN, ["guest", "registered"])
                ],
                actions={"preferred_carrier": ShippingCarrier.DELHIVERY.value},
                priority=2
            ),
            
            # FedEx for international
            FulfillmentRuleConfig(
                name="FedEx International",
                description="Use FedEx for international orders",
                rule_type=BusinessRuleType.CARRIER_SELECTION,
                conditions=[
                    RuleCondition("is_international", RuleConditionOperator.EQUALS, True)
                ],
                actions={"preferred_carrier": ShippingCarrier.FEDEX.value},
                priority=1
            )
        ]
    
    @staticmethod
    def get_insurance_rules() -> List[FulfillmentRuleConfig]:
        """Rules for insurance requirements"""
        return [
            # Insurance for high-value orders
            FulfillmentRuleConfig(
                name="High Value Insurance",
                description="Require insurance for orders over ₹5000",
                rule_type=BusinessRuleType.INSURANCE,
                conditions=[
                    RuleCondition("order_total", RuleConditionOperator.GREATER_THAN, 5000.0)
                ],
                actions={"require_insurance": True},
                priority=1
            )
        ]
    
    @staticmethod
    def get_signature_rules() -> List[FulfillmentRuleConfig]:
        """Rules for signature requirements"""
        return [
            # Signature for valuable orders
            FulfillmentRuleConfig(
                name="Signature Required",
                description="Require signature for orders over ₹2000",
                rule_type=BusinessRuleType.SIGNATURE,
                conditions=[
                    RuleCondition("order_total", RuleConditionOperator.GREATER_THAN, 2000.0)
                ],
                actions={"require_signature": True},
                priority=1
            )
        ]
    
    @staticmethod
    def get_priority_rules() -> List[FulfillmentRuleConfig]:
        """Rules for order priority"""
        return [
            # High priority for premium customers
            FulfillmentRuleConfig(
                name="Premium Customer Priority",
                description="High priority for premium customers",
                rule_type=BusinessRuleType.PRIORITY,
                conditions=[
                    RuleCondition("customer_type", RuleConditionOperator.EQUALS, "premium")
                ],
                actions={"priority": "high"},
                priority=1
            ),
            
            # High priority for large orders
            FulfillmentRuleConfig(
                name="Large Order Priority",
                description="High priority for orders over ₹15000",
                rule_type=BusinessRuleType.PRIORITY,
                conditions=[
                    RuleCondition("order_total", RuleConditionOperator.GREATER_THAN, 15000.0)
                ],
                actions={"priority": "high"},
                priority=2
            )
        ]

# ============================================================================
# RULE MANAGER
# ============================================================================

class FulfillmentRuleManager:
    """Manager for fulfillment rules"""
    
    def __init__(self):
        self.rules: List[FulfillmentRuleConfig] = []
        self._load_default_rules()
    
    def _load_default_rules(self):
        """Load all default rules"""
        default_rules = DefaultFulfillmentRules()
        
        self.rules.extend(default_rules.get_auto_fulfill_rules())
        self.rules.extend(default_rules.get_approval_rules())
        self.rules.extend(default_rules.get_carrier_selection_rules())
        self.rules.extend(default_rules.get_insurance_rules())
        self.rules.extend(default_rules.get_signature_rules())
        self.rules.extend(default_rules.get_priority_rules())
        
        # Sort by priority
        self.rules.sort(key=lambda r: r.priority)
        
        logger.info(f"Loaded {len(self.rules)} default fulfillment rules")
    
    def add_rule(self, rule: FulfillmentRuleConfig):
        """Add a new rule"""
        self.rules.append(rule)
        self.rules.sort(key=lambda r: r.priority)
    
    def remove_rule(self, rule_name: str) -> bool:
        """Remove a rule by name"""
        for i, rule in enumerate(self.rules):
            if rule.name == rule_name:
                del self.rules[i]
                return True
        return False
    
    def get_rule(self, rule_name: str) -> Optional[FulfillmentRuleConfig]:
        """Get a rule by name"""
        for rule in self.rules:
            if rule.name == rule_name:
                return rule
        return None
    
    def get_rules_by_type(self, rule_type: BusinessRuleType) -> List[FulfillmentRuleConfig]:
        """Get all rules of a specific type"""
        return [rule for rule in self.rules if rule.rule_type == rule_type]
    
    def get_active_rules(self) -> List[FulfillmentRuleConfig]:
        """Get all active rules"""
        return [rule for rule in self.rules if rule.is_active]
    
    def to_fulfillment_rules(self) -> List[FulfillmentRule]:
        """Convert all rules to FulfillmentRule objects"""
        return [rule.to_fulfillment_rule() for rule in self.get_active_rules()]
    
    def export_rules(self) -> str:
        """Export rules to JSON"""
        rules_data = []
        for rule in self.rules:
            rule_data = {
                'name': rule.name,
                'description': rule.description,
                'rule_type': rule.rule_type.value,
                'conditions': [
                    {
                        'field': c.field,
                        'operator': c.operator.value,
                        'value': c.value
                    } for c in rule.conditions
                ],
                'actions': rule.actions,
                'priority': rule.priority,
                'is_active': rule.is_active
            }
            rules_data.append(rule_data)
        
        return json.dumps(rules_data, indent=2)
    
    def import_rules(self, rules_json: str):
        """Import rules from JSON"""
        try:
            rules_data = json.loads(rules_json)
            
            for rule_data in rules_data:
                conditions = []
                for cond_data in rule_data['conditions']:
                    condition = RuleCondition(
                        field=cond_data['field'],
                        operator=RuleConditionOperator(cond_data['operator']),
                        value=cond_data['value']
                    )
                    conditions.append(condition)
                
                rule = FulfillmentRuleConfig(
                    name=rule_data['name'],
                    description=rule_data['description'],
                    rule_type=BusinessRuleType(rule_data['rule_type']),
                    conditions=conditions,
                    actions=rule_data['actions'],
                    priority=rule_data['priority'],
                    is_active=rule_data['is_active']
                )
                
                self.add_rule(rule)
            
            logger.info(f"Imported {len(rules_data)} rules")
            
        except Exception as e:
            logger.error(f"Error importing rules: {e}")
            raise

# Global rule manager instance
rule_manager = FulfillmentRuleManager()

def get_rule_manager() -> FulfillmentRuleManager:
    """Get the global rule manager instance"""
    return rule_manager
