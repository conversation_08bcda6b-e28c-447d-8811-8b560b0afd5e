"""
Inventory Prediction Model Package
==================================

This package contains the inventory prediction system components:
- inventory_model.py: Core inventory prediction model for demand forecasting
- inventory_model.pkl: Trained inventory prediction data

The inventory prediction system provides:
- Demand forecasting using ML ensemble methods
- Stock-out risk assessment
- Reorder point optimization
- Safety stock calculation
- Category-specific seasonality analysis
- Integration with e-commerce product catalog

Author: Allora Development Team
Date: 2025-07-12
"""

from .inventory_model import SeededDataInventoryModel

__all__ = [
    'SeededDataInventoryModel'
]

__version__ = "1.0.0"
__author__ = "Allora Development Team"
