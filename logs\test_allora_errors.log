2025-07-09 01:54:44 - test_basic - ERROR - This is an error message
2025-07-09 01:54:44 - test_basic - CRITICAL - This is a critical message
2025-07-09 01:54:44 - test_error - ERROR - Division by zero error occurred
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\allora_project\allora\backend\test_logging.py", line 129, in test_error_logging
    result = 1 / 0
ZeroDivisionError: division by zero
2025-07-09 01:54:44 - test_error - ERROR - JSON parsing failed
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\allora_project\allora\backend\test_logging.py", line 135, in test_error_logging
    data = json.loads("invalid json")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)
2025-07-09 02:00:47 - test_basic - ERROR - This is an error message
2025-07-09 02:00:47 - test_basic - CRITICAL - This is a critical message
2025-07-09 02:00:47 - test_error - ERROR - Division by zero error occurred
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\allora_project\allora\backend\test_logging.py", line 129, in test_error_logging
    result = 1 / 0
ZeroDivisionError: division by zero
2025-07-09 02:00:47 - test_error - ERROR - JSON parsing failed
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\allora_project\allora\backend\test_logging.py", line 135, in test_error_logging
    data = json.loads("invalid json")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)
