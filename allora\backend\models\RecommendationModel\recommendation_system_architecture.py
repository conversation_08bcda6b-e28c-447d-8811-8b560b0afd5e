"""
Advanced Product Recommendation System Architecture
==================================================

This module defines the comprehensive architecture for the Allora e-commerce
recommendation system with real-time personalization capabilities.

Architecture Components:
1. User Behavior Tracking System
2. Multiple ML Recommendation Algorithms
3. Real-time Personalization Engine
4. Recommendation Caching Layer
5. Analytics and Optimization System
6. API Management System

Author: Allora Development Team
Date: 2025-07-06
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple, Union
from enum import Enum
from datetime import datetime
import json

class RecommendationAlgorithm(Enum):
    """Supported recommendation algorithms"""
    COLLABORATIVE_FILTERING_USER = "collaborative_filtering_user"
    COLLABORATIVE_FILTERING_ITEM = "collaborative_filtering_item"
    CONTENT_BASED = "content_based"
    MATRIX_FACTORIZATION = "matrix_factorization"
    DEEP_LEARNING = "deep_learning"
    HYBRID = "hybrid"
    TRENDING = "trending"
    POPULARITY_BASED = "popularity_based"

class RecommendationType(Enum):
    """Types of recommendations"""
    PERSONALIZED = "personalized"
    SIMILAR_PRODUCTS = "similar_products"
    CROSS_SELL = "cross_sell"
    UPSELL = "upsell"
    TRENDING = "trending"
    NEW_ARRIVALS = "new_arrivals"
    SEASONAL = "seasonal"
    CATEGORY_BASED = "category_based"
    BRAND_BASED = "brand_based"
    PRICE_BASED = "price_based"

class UserInteractionType(Enum):
    """Types of user interactions"""
    VIEW = "view"
    CLICK = "click"
    PURCHASE = "purchase"
    ADD_TO_CART = "add_to_cart"
    REMOVE_FROM_CART = "remove_from_cart"
    WISHLIST_ADD = "wishlist_add"
    WISHLIST_REMOVE = "wishlist_remove"
    RATING = "rating"
    REVIEW = "review"
    SHARE = "share"
    SEARCH = "search"
    COMPARE = "compare"

@dataclass
class UserInteraction:
    """Represents a user interaction with the system"""
    user_id: str
    interaction_type: UserInteractionType
    item_id: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    value: Optional[float] = None  # Rating, price, etc.
    context: Dict[str, Any] = field(default_factory=dict)
    session_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class UserProfile:
    """Comprehensive user profile for personalization"""
    user_id: str
    preferences: Dict[str, float] = field(default_factory=dict)
    categories: Dict[str, float] = field(default_factory=dict)
    brands: Dict[str, float] = field(default_factory=dict)
    price_range: Tuple[float, float] = (0.0, float('inf'))
    interaction_history: List[UserInteraction] = field(default_factory=list)
    demographics: Dict[str, Any] = field(default_factory=dict)
    behavioral_segments: List[str] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.utcnow)

@dataclass
class RecommendationRequest:
    """Request for recommendations"""
    user_id: str
    recommendation_type: RecommendationType
    limit: int = 10
    context: Dict[str, Any] = field(default_factory=dict)
    filters: Dict[str, Any] = field(default_factory=dict)
    exclude_items: List[str] = field(default_factory=list)
    algorithm_preference: Optional[RecommendationAlgorithm] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)

@dataclass
class RecommendationResult:
    """Result of a recommendation request"""
    user_id: str
    recommendations: List[Tuple[str, float]]  # (item_id, score)
    algorithm: str
    context: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    confidence_score: float = 0.0
    explanation: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RecommendationResponse:
    """API response for recommendations"""
    success: bool
    recommendations: List[Dict[str, Any]]
    total_count: int
    algorithm_used: str
    processing_time_ms: float
    user_id: str
    request_id: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None

class RecommendationSystemConfig:
    """Configuration for the recommendation system"""
    
    # Algorithm weights for hybrid recommendations
    ALGORITHM_WEIGHTS = {
        RecommendationAlgorithm.COLLABORATIVE_FILTERING_USER: 0.3,
        RecommendationAlgorithm.COLLABORATIVE_FILTERING_ITEM: 0.25,
        RecommendationAlgorithm.CONTENT_BASED: 0.2,
        RecommendationAlgorithm.MATRIX_FACTORIZATION: 0.15,
        RecommendationAlgorithm.DEEP_LEARNING: 0.1
    }
    
    # Caching configuration
    CACHE_TTL_SECONDS = {
        RecommendationType.PERSONALIZED: 3600,  # 1 hour
        RecommendationType.TRENDING: 1800,      # 30 minutes
        RecommendationType.SIMILAR_PRODUCTS: 7200,  # 2 hours
        RecommendationType.CATEGORY_BASED: 3600,    # 1 hour
    }
    
    # Performance thresholds
    MAX_PROCESSING_TIME_MS = 500
    MIN_CONFIDENCE_SCORE = 0.3
    DEFAULT_RECOMMENDATION_COUNT = 10
    MAX_RECOMMENDATION_COUNT = 50
    
    # User behavior tracking
    INTERACTION_WEIGHTS = {
        UserInteractionType.PURCHASE: 5.0,
        UserInteractionType.ADD_TO_CART: 3.0,
        UserInteractionType.WISHLIST_ADD: 2.0,
        UserInteractionType.RATING: 2.5,
        UserInteractionType.VIEW: 1.0,
        UserInteractionType.CLICK: 1.5,
        UserInteractionType.SHARE: 2.0
    }
    
    # Model update frequencies
    MODEL_UPDATE_INTERVALS = {
        'user_profiles': 3600,      # 1 hour
        'item_similarities': 86400,  # 24 hours
        'trending_items': 1800,     # 30 minutes
        'collaborative_models': 86400  # 24 hours
    }

class RecommendationSystemArchitecture:
    """Main architecture class defining system components and workflows"""
    
    def __init__(self):
        self.config = RecommendationSystemConfig()
    
    def get_system_components(self) -> Dict[str, str]:
        """Get all system components and their descriptions"""
        return {
            "behavior_tracker": "Tracks and analyzes user interactions in real-time",
            "ml_models": "Multiple ML algorithms for different recommendation scenarios",
            "personalization_engine": "Real-time user profile management and preference learning",
            "cache_layer": "Redis-based caching for fast recommendation retrieval",
            "api_system": "RESTful APIs for recommendation requests and management",
            "analytics_system": "Performance monitoring and optimization recommendations",
            "model_manager": "ML model training, evaluation, and deployment management",
            "a_b_testing": "A/B testing framework for recommendation algorithm comparison"
        }
    
    def get_data_flow(self) -> Dict[str, List[str]]:
        """Get the data flow between system components"""
        return {
            "user_interaction": [
                "behavior_tracker",
                "personalization_engine",
                "analytics_system"
            ],
            "recommendation_request": [
                "api_system",
                "cache_layer",
                "personalization_engine",
                "ml_models",
                "response_formatter"
            ],
            "model_training": [
                "data_collector",
                "feature_engineer",
                "model_trainer",
                "model_evaluator",
                "model_deployer"
            ],
            "analytics_pipeline": [
                "interaction_collector",
                "metrics_calculator",
                "report_generator",
                "optimization_recommender"
            ]
        }
    
    def get_algorithm_details(self) -> Dict[str, Dict[str, Any]]:
        """Get detailed information about each recommendation algorithm"""
        return {
            "collaborative_filtering_user": {
                "description": "Finds users with similar preferences and recommends items they liked",
                "use_case": "When you have sufficient user interaction data",
                "pros": ["Good for discovering new items", "Works well with implicit feedback"],
                "cons": ["Cold start problem", "Sparsity issues"],
                "data_requirements": "User-item interaction matrix with sufficient density"
            },
            "collaborative_filtering_item": {
                "description": "Recommends items similar to those the user has interacted with",
                "use_case": "When item relationships are more stable than user preferences",
                "pros": ["More stable than user-based", "Easier to explain"],
                "cons": ["Limited diversity", "Cold start for new items"],
                "data_requirements": "Item-item similarity matrix based on user interactions"
            },
            "content_based": {
                "description": "Recommends items with similar features to user's preferences",
                "use_case": "When you have rich item metadata and user profiles",
                "pros": ["No cold start problem", "Transparent recommendations"],
                "cons": ["Limited diversity", "Requires good feature engineering"],
                "data_requirements": "Rich item features and user preference profiles"
            },
            "matrix_factorization": {
                "description": "Uses latent factors to model user-item interactions",
                "use_case": "Large-scale systems with sparse interaction data",
                "pros": ["Handles sparsity well", "Scalable", "Good performance"],
                "cons": ["Black box model", "Requires parameter tuning"],
                "data_requirements": "User-item interaction matrix"
            },
            "deep_learning": {
                "description": "Neural networks for complex pattern recognition",
                "use_case": "When you have large amounts of diverse data",
                "pros": ["Can capture complex patterns", "Handles multiple data types"],
                "cons": ["Requires large datasets", "Computationally expensive"],
                "data_requirements": "Large-scale multi-modal data"
            },
            "hybrid": {
                "description": "Combines multiple algorithms for optimal results",
                "use_case": "Production systems requiring robust performance",
                "pros": ["Best of all algorithms", "Robust performance"],
                "cons": ["Complex implementation", "Harder to debug"],
                "data_requirements": "All component algorithm requirements"
            }
        }

# Export main classes and enums
__all__ = [
    'RecommendationAlgorithm',
    'RecommendationType', 
    'UserInteractionType',
    'UserInteraction',
    'UserProfile',
    'RecommendationRequest',
    'RecommendationResult',
    'RecommendationResponse',
    'RecommendationSystemConfig',
    'RecommendationSystemArchitecture'
]
