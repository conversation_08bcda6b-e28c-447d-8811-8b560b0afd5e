"""
RMA (Return Merchandise Authorization) System
============================================

Comprehensive RMA system for handling returns, exchanges, and refunds
with full workflow automation, approval processes, and integration with
existing order fulfillment and inventory management systems.

Components:
- rma_architecture.py: System architecture and data models
- rma_engine.py: Core business logic and processing engine
- rma_api.py: Flask API endpoints for RMA management
- rma_utils.py: Utility functions and helpers

Features:
- Complete RMA workflow management
- Automated approval processes
- Integration with orders, inventory, and payments
- Customer and admin APIs
- Analytics and reporting
- Document management
- Timeline tracking

Author: Allora Development Team
Date: 2025-07-12
"""

from .rma_architecture import (
    RMAStatus, RMAType, ReturnReason, InspectionResult, RefundMethod,
    RMAConfiguration, RMAItem, RMARequest, RMAWorkflowRule,
    DEFAULT_RMA_RULES, RMA_CONFIG
)

from .rma_engine import (
    RMAEngine, RMAProcessingError, RMAValidationError
)

from .rma_api import rma_bp

from .rma_utils import (
    RMAValidator, RMACalculator, RMAReportGenerator, 
    RMANotificationHelper, RMAIntegrationHelper
)

__all__ = [
    # Architecture components
    'RMAStatus',
    'RMAType', 
    'ReturnReason',
    'InspectionResult',
    'RefundMethod',
    'RMAConfiguration',
    'RMAItem',
    'RMARequest',
    'RMAWorkflowRule',
    'DEFAULT_RMA_RULES',
    'RMA_CONFIG',
    
    # Engine components
    'RMAEngine',
    'RMAProcessingError',
    'RMAValidationError',
    
    # API components
    'rma_bp',
    
    # Utility components
    'RMAValidator',
    'RMACalculator', 
    'RMAReportGenerator',
    'RMANotificationHelper',
    'RMAIntegrationHelper'
]

__version__ = "1.0.0"
__author__ = "Allora Development Team"
