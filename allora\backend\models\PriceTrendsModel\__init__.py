"""
Price Trends Model Package
==========================

This package contains the price trends prediction system components:
- price_trends_model.py: Core price trends model for price forecasting
- price_trends_model.pkl: Trained price trends prediction data

The price trends system provides:
- Price forecasting using ML ensemble methods
- Technical indicators analysis (Moving averages, RSI, MACD)
- Category-specific volatility patterns
- Trend analysis (increasing, decreasing, stable)
- Confidence scoring for predictions
- Integration with e-commerce product catalog

Author: Allora Development Team
Date: 2025-07-12
"""

from .price_trends_model import SeededDataPriceTrendsModel

__all__ = [
    'SeededDataPriceTrendsModel'
]

__version__ = "1.0.0"
__author__ = "Allora Development Team"
